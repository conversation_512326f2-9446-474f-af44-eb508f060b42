/* eslint-disable @typescript-eslint/no-var-requires */
import { defineConfig } from 'cypress';
import createBundler from '@bahmutov/cypress-esbuild-preprocessor';
import cypressFailFast from 'cypress-fail-fast/plugin';

export default defineConfig({
  projectId: '3ib6um',
  viewportWidth: 1280,
  viewportHeight: 1280,
  defaultCommandTimeout: 60000,
  requestTimeout: 100000,
  chromeWebSecurity: false,
  retries: {
    runMode: 2,
    openMode: 0,
  },
  blockHosts: [
    'assets.adobedtm.com',
    'dpm.demdex.net',
    'fast.qantas.demdex.net',
    'www.google-analytics.com',
    'qantasairways.tt.omtrdc.net',
    'ci-mpsnare.iovation.com',
    'www.googletagmanager.com',
    'googleads.g.doubleclick.net',
    'jnn-pa.googleapis.com',
    'play.google.com',
    'a26148840652.cdn.optimizely.com',
    'o98201.ingest.sentry.io',
  ],
  env: { TEST_PROPERTY_ID: 210830, AVA_URL: 'https://staging-availability-search-api.nonprod.jqdev.net' },
  e2e: {
    setupNodeEvents(on, config) {
      on('file:preprocessor', createBundler());
      cypressFailFast(on, config);
      return require('./cypress/plugins/index.js')(on, config);
    },
    baseUrl: 'https://staging-hotels-qantas-akamai.jqdev.net',
    specPattern: 'cypress/e2e/**/*.spec.js',
    experimentalRunAllSpecs: true,
    experimentalStudio: true,
  },
});
