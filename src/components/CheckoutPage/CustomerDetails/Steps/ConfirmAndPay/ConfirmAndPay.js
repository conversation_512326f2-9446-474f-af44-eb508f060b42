import React, { Fragment, useState, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import noop from 'lodash/noop';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { Box, Button, Checkbox, Flex, Heading, Label, NakedButton, Text } from '@qga/roo-ui/components';
import { StepHeader, EDITING } from 'components/StepWizard';
import { getIsCreditCardValid, getRequiresCreditCardPayment, getIsPaymentMethodChanged } from 'store/checkout/checkoutSelectors';
import { createBooking } from 'store/booking/bookingActions';
import { mediaQuery } from 'lib/styledSystem';
import { getStayDates, getQuote } from 'store/quote/quoteSelectors';
import { getIsCreating } from 'store/booking/bookingSelectors';
import theme from 'lib/theme';
import BookingProgressDialog from './BookingProgressDialog';
import PriceDetails from './PriceDetails';
import { useDataLayer } from 'hooks/useDataLayer';
import Markup from 'components/Markup';
import { useModal } from 'lib/hooks';
import Modal from 'components/Modal';
import CreditCardIcons from './CreditCardIcons';
import BookingSummary from './BookingSummary';
import { PRIVACY_AND_SECURITY_URL } from 'config';
import TermsAndConditionsModal from './TermsAndConditionsModal';
import ExternalLink from 'components/ExternalLink';
import PaymentMethodChanged from './PaymentMethodChanged';
import useAddPaymentInfoEvent from 'hooks/useAddPaymentInfoEvent';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
// TODO: remove CreditCard and related logic once AdyenDropIn is release
import CreditCard from './CreditCard';
import CreditCardError from './CreditCardError';
import AdyenDropIn from 'components/AdyenDropIn';
import ErrorAlert from 'components/CheckoutPage/ErrorAlert';
import usePaymentMethods from 'components/AdyenDropIn/hooks/usePaymentMethods';
import useAdyenDropIn from './hooks/useAdyenDropIn';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';

const AcceptTermsText = styled(Text)`
  color: ${themeGet('colors.greys.charcoal')};
  display: inline-block;
  font-size: ${themeGet('fontSizes.base')};
  font-weight: ${themeGet('fontWeights.normal')};
  height: inherit;
  width: auto;
  margin: 0 ${themeGet('space.1')} 0 0;

  ${mediaQuery.mobileOnlyEscapeHatch} {
    font-size: ${themeGet('fontSizes.sm')};
  }
`;

const TermsAndConditionsLink = styled(ExternalLink)`
  color: ${themeGet('colors.greys.charcoal')};
  font-size: ${themeGet('fontSizes.base')};
  font-weight: ${themeGet('fontWeights.normal')};
  text-decoration: underline;
  flex-direction: column;

  margin: 2px ${themeGet('space.1')} 0 0;

  ${mediaQuery.mobileOnlyEscapeHatch} {
    font-size: ${themeGet('fontSizes.sm')};
  }
`;

const TermsAndConditionsLabel = styled(Label)`
  line-height: 1.3;
`;

const ConfirmAndPay = ({ step, ...rest }) => {
  const { isAdyenDropIn } = useAdyenDropIn();
  const dropInRef = useRef(null);
  const { isPaymentMethodsReady, qepgVault, paymentMethods, storedPaymentMethods } = usePaymentMethods();

  const [isTermsAccepted, setTermsAccepted] = useState(false);
  const [termsNotAcceptedError, setTermsNotAcceptedError] = useState(false);
  const confirmBookingRef = useRef();

  const { containerRef, hasState, edit, editPrevStep, stepNumber } = step;
  const { openModal, modalProps } = useModal();

  const dispatch = useDispatch();
  const isCreating = useSelector(getIsCreating);
  const { property, offer } = useSelector(getQuote) || {};
  const { emitInteractionEvent } = useDataLayer();
  const isCreditCardValid = useSelector(getIsCreditCardValid);
  const requiresCreditCardPayment = useSelector(getRequiresCreditCardPayment);
  const { checkIn, checkOut } = useSelector(getStayDates);
  const isPaymentMethodChanged = useSelector(getIsPaymentMethodChanged);

  const { showMessage, max_rooms_cutoff } = useAvailableRoomsMessage();

  const { isPriceStrikethrough } = usePriceStrikethrough();

  const toggleTermsAccepted = useCallback(() => {
    setTermsNotAcceptedError(false);
    const newTermsAccepted = !isTermsAccepted;
    setTermsAccepted(newTermsAccepted);
    emitInteractionEvent({ type: 'Policy and Terms Links', value: `Agree Opted ${newTermsAccepted ? 'In' : 'Out'}` });
  }, [isTermsAccepted, emitInteractionEvent]);

  const handlePrivacyPolicyClicked = useCallback(() => {
    emitInteractionEvent({ type: 'Policy and Terms Links', value: 'Privacy Policy Selected' });
  }, [emitInteractionEvent]);

  const openCancellationPolicy = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Policy and Terms Links', value: 'Hotel Cancellation Policy Selected' });
  }, [emitInteractionEvent, openModal]);

  const submitBookingAdyenDropIn = useCallback(() => {
    setTermsNotAcceptedError(false);

    if (requiresCreditCardPayment) {
      if (!dropInRef?.current) {
        return;
      }

      if (!dropInRef.current.isValid()) {
        dropInRef.current.validate();

        return;
      }
    }

    if (!isTermsAccepted) {
      setTermsNotAcceptedError(true);
      confirmBookingRef?.current.scrollIntoView({ behavior: 'smooth' });

      return;
    }

    dropInRef?.current?.updatePayments?.();

    dispatch(
      createBooking({
        showMessage,
        max_rooms_cutoff,
        isPriceStrikethrough,
      }),
    );
    emitInteractionEvent({ type: 'Pay Now Button', value: 'Button Selected' });
  }, [
    requiresCreditCardPayment,
    dropInRef,
    isTermsAccepted,
    dispatch,
    showMessage,
    max_rooms_cutoff,
    isPriceStrikethrough,
    emitInteractionEvent,
  ]);

  const submitBooking = useCallback(() => {
    if (isCreditCardValid) {
      dispatch(
        createBooking({
          showMessage,
          max_rooms_cutoff,
          isPriceStrikethrough,
        }),
      );
      emitInteractionEvent({ type: 'Pay Now Button', value: 'Button Selected' });
    }
  }, [isCreditCardValid, dispatch, showMessage, max_rooms_cutoff, isPriceStrikethrough, emitInteractionEvent]);

  const isSubmitDisabled = isAdyenDropIn ? isCreating : isCreating || !isTermsAccepted || !isCreditCardValid;
  const stepSubTitle = !isAdyenDropIn && requiresCreditCardPayment ? <CreditCardIcons /> : null;

  useAddPaymentInfoEvent();

  return (
    <Fragment>
      <Box bg="white" borderRadius="default" boxShadow="hard" ref={containerRef} {...rest}>
        <StepHeader title="Confirm & pay" subTitle={stepSubTitle} edit={edit} hasState={hasState} stepNumber={stepNumber} />
        {hasState(EDITING) && (
          <Fragment>
            {requiresCreditCardPayment && (
              <Box p={isAdyenDropIn ? [0, 4] : [4, 8]}>
                {isPaymentMethodChanged && (
                  <Box p={isAdyenDropIn ? [4, 0] : [0]}>
                    <PaymentMethodChanged editPrevStep={editPrevStep} />
                  </Box>
                )}
                {isAdyenDropIn ? (
                  <>
                    <CreditCardError />
                    {qepgVault && storedPaymentMethods && (
                      <Heading.h3 fontSize="md" paddingLeft={4} mb={0} mt={[4, 2]} lineHeight="32px">
                        Payment method
                      </Heading.h3>
                    )}
                    {isPaymentMethodsReady && (
                      <AdyenDropIn
                        ref={dropInRef}
                        paymentMethods={paymentMethods}
                        storedPaymentMethods={storedPaymentMethods}
                        qepgVault={qepgVault}
                      />
                    )}
                  </>
                ) : (
                  <CreditCard />
                )}
              </Box>
            )}
            <Box p={[4, 8]} borderTop={1} borderColor="greys.alto" ref={confirmBookingRef}>
              <Heading.h3 fontSize="md">Please confirm your booking details.</Heading.h3>
              <BookingSummary />
              <PriceDetails property={property} offer={offer} checkIn={checkIn} checkOut={checkOut} />

              <Flex mt={8}>
                <Box>
                  <TermsAndConditionsLabel htmlfor="acceptTerms" mb={[4, 0]}>
                    <Checkbox
                      aria-label="I agree to terms and conditions, hotel cancellation policy and Privacy Policy"
                      data-testid="accept-terms"
                      id="acceptTerms"
                      name="acceptTerms"
                      checked={isTermsAccepted}
                      onChange={toggleTermsAccepted}
                    />
                    <Text hidden>I agree to terms and conditions, hotel cancellation policy and Privacy Policy</Text>
                  </TermsAndConditionsLabel>
                </Box>
                <Flex flexWrap="wrap" alignItems="baseline">
                  <AcceptTermsText>I agree to</AcceptTermsText>
                  <TermsAndConditionsModal />
                  <TermsAndConditionsLink
                    as={NakedButton}
                    onClick={openCancellationPolicy}
                    hoverColor="inherit"
                    data-testid="cancellation-policy-button"
                    aria-label="View hotel cancellation policy"
                  >
                    hotel cancellation policy
                  </TermsAndConditionsLink>
                  <Text fontSize={['sm', 'base']} mr={1}>
                    and
                  </Text>
                  <TermsAndConditionsLink
                    onClick={handlePrivacyPolicyClicked}
                    href={PRIVACY_AND_SECURITY_URL}
                    rel="noopener noreferrer"
                    target="_blank"
                    aria-label="View privacy policy in a new tab"
                  >
                    Privacy Policy.
                  </TermsAndConditionsLink>
                </Flex>
              </Flex>
            </Box>
            <Box p={[4, 8]} borderTop={`1px solid ${theme.colors.greys.alto}`}>
              {termsNotAcceptedError && (
                <Box mb={6}>
                  <ErrorAlert
                    heading="Accept terms and conditions to continue"
                    description="Please confirm you have read and accepted the terms and conditions to proceed with your booking."
                  />
                </Box>
              )}
              <Button
                onClick={isAdyenDropIn ? submitBookingAdyenDropIn : submitBooking}
                disabled={isSubmitDisabled}
                variant="primary"
                data-testid="payment-button"
                width={['100%', 'auto']}
              >
                Pay Now
              </Button>
              <Box mt={3}>
                <Text color="greys.steel">Your payment will be processed in Australia</Text>
              </Box>
            </Box>
          </Fragment>
        )}
      </Box>
      {isCreating && <BookingProgressDialog />}
      <Modal {...modalProps} title="Cancellation policy">
        <Text display="block" mb="5" data-testid="cancellation-policy-description">
          <Heading.h4 fontSize="sm">Cancellation policy</Heading.h4>
          <Markup content={offer.cancellationPolicy.description} />
        </Text>
      </Modal>
    </Fragment>
  );
};

ConfirmAndPay.propTypes = {
  step: PropTypes.shape({
    data: PropTypes.object,
    containerRef: PropTypes.shape({ current: PropTypes.any }),
    hasState: PropTypes.func,
    edit: PropTypes.func,
    editPrevStep: PropTypes.func,
    stepNumber: PropTypes.number,
  }),
};

ConfirmAndPay.defaultProps = {
  step: {
    data: {},
    containerRef: { current: null },
    hasState: noop,
    edit: noop,
    editPrevStep: noop,
  },
};

export default ConfirmAndPay;
