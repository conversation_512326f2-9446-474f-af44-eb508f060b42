import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import * as sessionStorage from 'lib/browser/sessionStorage';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { useUnmount } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import { Box, Container, Flex, Heading } from '@qga/roo-ui/components';
import SearchResultFetcher from 'components/SearchResultFetcher';
import PromoAreaDealsFetcher from 'components/PromoAreaDealsFetcher';
import CampaignMessaging from 'components/CampaignMessaging';
import MapButton from './Sidebar/MapButton';
import SkipToContentButton from 'components/SkipToContentButton';
import WelcomeMessage from 'components/WelcomeMessage';
import SearchControls from './SearchControls';
import { SearchResultSummaryConnected } from './SearchResultSummary';
import SortSelect from './SortSelect';
import MobileSortSelect from './MobileSearchSelect';
import SidebarFilters from './Sidebar/Filters';
import SidebarContainer from './Sidebar/SidebarContainer';
import SearchList from './SearchList';
import PayWith from './PayWith';
import SearchError from './SearchError';
import DynamicMessageBox from './Messaging/DynamicMessageBox';
import ListSearchMeta from './ListSearchMeta';
import PhoneShortcutFilters from './PhoneShortcutFilters';
import PromoArea from './PromoArea';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';
import { clearResults, updateQuery } from 'store/search/searchActions';
import RequestCallbackModal from '../RequestCallbackModal';
import { clearPointsLevels } from 'store/pointsConversion/pointsConversionActions';
import { getSearchQuery } from 'store/search/searchSelectors';
import { UpdateNavigationIconClose } from 'lib/qta/qta';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import useListSearchGa4Event from 'hooks/useListSearchGa4Event';
import { useBreakpoints } from 'hooks/useBreakpoints';

const CollapsibleBox = styled(Box)`
  flex-shrink: 0;
`;

const MainContent = styled(Box)`
  flex: 1;
  position: relative;
`;

const DisplaySidebarFilters = styled(Box)`
  position: absolute;
  top: 60px;
  left: 0;
  z-index: ${themeGet('zIndices.modalContent')};
  display: ${(props) => (props.isSidebarOpen ? 'block' : 'none')};
  width: ${themeGet('uiStructure.mediumScreenSidebar')};
`;

DisplaySidebarFilters.displayName = 'DisplaySidebarFilters';

MainContent.displayName = 'MainContent';

const ListSearchLayout = () => {
  const dispatch = useDispatch();
  const query = useSelector(getSearchQuery);
  const isMobileApp = useSelector(getIsMobileApp);
  const isMobile = useBreakpoints().isLessThanBreakpoint(0);
  const router = useRouter();
  const titleText = `Hotels in ${query.location}`;

  // refresh the query state for the first page here so that the useEffect in
  // SearchResultFetcher and PromoAreaDealsFetcher triggers when the
  // hotels tab is selected from the search page
  useEffect(() => {
    if (!query.page || query.page === 1) {
      dispatch(updateQuery(query));
    }
  }, [dispatch, query]);

  useEffect(() => {
    const scrolledHeight = sessionStorage.get('scrolledHeight');
    const isFromPropertyPage = router.query.backToSearch || sessionStorage.get('isFromPropertyPage');
    const doNotScroll = !isMobile || !scrolledHeight || !isFromPropertyPage;

    window.sessionStorage.removeItem('scrolledHeight');
    window.sessionStorage.removeItem('isFromPropertyPage');

    if (doNotScroll) return;

    window.scroll({
      top: scrolledHeight,
      behavior: 'smooth',
    });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useUnmount(() => {
    dispatch(clearResults());
    dispatch(clearPointsLevels());
  });

  useListSearchGa4Event();

  return (
    <>
      <RequestCallbackModal interactionType="Multiroom booking" />
      <SearchResultFetcher />
      <LegacyBrowserBoundary>
        <PromoAreaDealsFetcher />
      </LegacyBrowserBoundary>
      {isMobileApp && <UpdateNavigationIconClose />}
      <ListSearchMeta />
      <WelcomeMessage />
      <CampaignMessaging />
      <SearchControls />
      <LegacyBrowserBoundary>{!isMobile && <PromoArea />}</LegacyBrowserBoundary>
      <Container>
        <DynamicMessageBox />
      </Container>
      <div id="main-content"></div>
      <PhoneShortcutFilters />
      <MobileSortSelect />
      <Container display={['none', 'block']}>
        <Box minHeight="80px" my={[2, 3, 8]}>
          <Heading.h1 data-testid="location-header">{titleText}</Heading.h1>
          <SearchResultSummaryConnected data-testid="search-result-summary-desktop" />
        </Box>
      </Container>
      <Container>
        <Flex mt={[0, 4, 4]} flex="1 1 auto">
          <CollapsibleBox flexBasis={[0, 0, 400]}>
            <Box display={['none', 'none', 'block']}>
              <SidebarContainer position="relative">
                <SkipToContentButton as="a" href="#search-results">
                  Skip to content
                </SkipToContentButton>
                <MapButton />
                <SidebarFilters />
              </SidebarContainer>
            </Box>
          </CollapsibleBox>
          <div id="search-results" />
          <MainContent data-testid="search-results">
            <Flex alignItems={['flex-start', 'flex-end']} display={['none', 'flex']} mb={10}>
              <SortSelect />
              <PayWith name="payWith" />
            </Flex>
            <SearchError />
            <SearchList />
          </MainContent>
        </Flex>
      </Container>
    </>
  );
};

export default ListSearchLayout;
