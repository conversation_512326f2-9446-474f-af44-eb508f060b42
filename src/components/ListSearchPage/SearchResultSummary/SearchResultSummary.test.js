import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SearchResultSummary } from './SearchResultSummary';

describe('SearchResultSummary', () => {
  const defaultProps = {
    resultCount: 0,
    totalPropertyCount: 0,
    isLoading: false,
  };

  const renderComponent = (props = {}) => {
    return render(<SearchResultSummary {...defaultProps} {...props} />);
  };

  describe('result count visibility', () => {
    it('hides result count when loading', () => {
      renderComponent({ isLoading: true, resultCount: 5, totalPropertyCount: 15 });
      expect(screen.getByTestId('result-count')).not.toBeVisible();
    });

    it('shows result count when not loading', () => {
      renderComponent({ isLoading: false, resultCount: 10, totalPropertyCount: 20 });
      expect(screen.getByTestId('result-count')).toHaveTextContent('Showing 10 available out of 20 properties');
    });
  });

  describe('result count formatting', () => {
    it('includes "Showing" prefix when there are results', () => {
      renderComponent({ resultCount: 10, totalPropertyCount: 20 });
      expect(screen.getByTestId('result-count')).toHaveTextContent('Showing 10 available out of 20 properties');
    });

    it('excludes "Showing" prefix when no results', () => {
      renderComponent({ resultCount: 0, totalPropertyCount: 0 });
      expect(screen.getByTestId('result-count')).toHaveTextContent('0 available out of 0 properties');
    });

    it('handles single result', () => {
      renderComponent({ resultCount: 1, totalPropertyCount: 1 });
      expect(screen.getByTestId('result-count')).toHaveTextContent('Showing 1 available out of 1 properties');
    });

    it('handles large numbers', () => {
      renderComponent({ resultCount: 999, totalPropertyCount: 1500 });
      expect(screen.getByTestId('result-count')).toHaveTextContent('Showing 999 available out of 1500 properties');
    });
  });
});
