import React from 'react';
import { useSelector } from 'react-redux';
import { getIsLoading, getResultCount, getTotalPropertyCount } from 'store/search/searchSelectors';
import { SearchResultSummary } from './SearchResultSummary';

export const SearchResultSummaryConnected = React.memo(() => {
  const resultCount = useSelector(getResultCount);
  const totalPropertyCount = useSelector(getTotalPropertyCount);
  const isLoading = useSelector(getIsLoading);
  const isNotReady = isLoading === undefined || resultCount === undefined;

  if (isNotReady) return null;
  return <SearchResultSummary resultCount={resultCount} totalPropertyCount={totalPropertyCount} isLoading={isLoading} />;
});

SearchResultSummaryConnected.displayName = 'SearchResultSummaryConnected';

export default SearchResultSummaryConnected;
