import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from 'redux-mock-store';
import { SearchResultSummaryConnected } from './SearchResultSummaryConnected';
import { getResultCount, getTotalPropertyCount, getIsLoading } from 'store/search/searchSelectors';

jest.mock('store/search/searchSelectors');

jest.mock('./SearchResultSummary', () => ({
  SearchResultSummary: (props) => <div data-testid="search-result-summary" data-props={JSON.stringify(props)} />,
}));

const mockStore = configureStore([]);
const store = mockStore({});

const renderComponent = () => {
  return render(
    <Provider store={store}>
      <SearchResultSummaryConnected />
    </Provider>,
  );
};

const setupMocks = ({ resultCount, totalPropertyCount, isLoading }) => {
  getResultCount.mockReturnValue(resultCount);
  getTotalPropertyCount.mockReturnValue(totalPropertyCount);
  getIsLoading.mockReturnValue(isLoading);
};

const expectComponentToRenderWithProps = (expectedProps) => {
  const component = screen.getByTestId('search-result-summary');
  expect(component).toBeInTheDocument();

  const actualProps = JSON.parse(component.getAttribute('data-props'));
  expect(actualProps).toEqual(expectedProps);
};

const expectComponentNotToRender = () => {
  expect(screen.queryByTestId('search-result-summary')).not.toBeInTheDocument();
};

beforeEach(() => {
  jest.clearAllMocks();
});

describe('when component should render', () => {
  it.each([
    {
      scenario: 'with typical search results',
      props: { resultCount: 10, totalPropertyCount: 99, isLoading: false },
    },
    {
      scenario: 'when loading',
      props: { resultCount: 5, totalPropertyCount: 50, isLoading: true },
    },
    {
      scenario: 'with no results found',
      props: { resultCount: 0, totalPropertyCount: 0, isLoading: false },
    },
  ])('renders $scenario', ({ props }) => {
    setupMocks(props);
    renderComponent();
    expectComponentToRenderWithProps(props);
  });
});

describe('when component should not render', () => {
  it.each([
    {
      scenario: 'resultCount is undefined but isLoading is defined',
      props: { resultCount: undefined, totalPropertyCount: 25, isLoading: false },
    },
    {
      scenario: 'isLoading is undefined but resultCount is defined',
      props: { resultCount: 8, totalPropertyCount: 40, isLoading: undefined },
    },
    {
      scenario: 'both resultCount and isLoading are undefined',
      props: { resultCount: undefined, totalPropertyCount: 0, isLoading: undefined },
    },
  ])('does not render when $scenario', ({ props }) => {
    setupMocks(props);
    renderComponent();
    expectComponentNotToRender();
  });
});
