import React from 'react';
import PropTypes from 'prop-types';
import Summary from './components/Summary';

export const SearchResultSummary = ({ resultCount, totalPropertyCount, isLoading }) => {
  return <Summary isLoading={isLoading} resultCount={resultCount} totalPropertyCount={totalPropertyCount} />;
};

SearchResultSummary.propTypes = {
  isLoading: PropTypes.bool,
  resultCount: PropTypes.number,
  totalPropertyCount: PropTypes.number,
};

SearchResultSummary.defaultProps = {
  isLoading: false,
  resultCount: 0,
  totalPropertyCount: 0,
};
