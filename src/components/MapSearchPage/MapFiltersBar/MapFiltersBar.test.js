import React from 'react';
import '@testing-library/jest-dom';
import MapFiltersBar from './MapFiltersBar';
import { renderWithProviders } from 'test-utils/reactUtils';

jest.mock('components/MapSearchPage/Header', () => () => <div data-testid="Header">Header</div>);
jest.mock('./SearchSummary', () => () => <div data-testid="SearchSummary">SearchSummary</div>);
jest.mock('./items/PriceFilterItem', () => () => <div data-testid="PriceFilterItem">PriceFilterItem</div>);
jest.mock('./items/PaymentPolicyFilterItem', () => () => <div data-testid="PaymentPolicyFilterItem">PaymentPolicyFilterItem</div>);
jest.mock('./items/FacilityFilterItem', () => () => <div data-testid="FacilityFilterItem">FacilityFilterItem</div>);
jest.mock('./items/PropertyTypeFilterItem', () => () => <div data-testid="PropertyTypeFilterItem">PropertyTypeFilterItem</div>);
jest.mock('./items/StarRatingFilterItem', () => () => <div data-testid="StarRatingFilterItem">StarRatingFilterItem</div>);
jest.mock('./items/TripadvisorRatingFilterItem', () => () => (
  <div data-testid="TripadvisorRatingFilterItem">TripadvisorRatingFilterItem</div>
));
jest.mock('components/Filters/Search/ClearFilters', () => () => <div data-testid="ClearFilters">ClearFilters</div>);

describe('<MapFiltersBar /> renders all the components', () => {
  const expectedComponents = [
    'Header',
    'SearchSummary',
    'PriceFilterItem',
    'PaymentPolicyFilterItem',
    'FacilityFilterItem',
    'PropertyTypeFilterItem',
    'StarRatingFilterItem',
    'TripadvisorRatingFilterItem',
    'ClearFilters',
  ];

  test.each(expectedComponents)('%s is rendered', (componentId) => {
    const { getByTestId } = renderWithProviders(<MapFiltersBar />);
    expect(getByTestId(componentId)).toBeInTheDocument();
  });
});
