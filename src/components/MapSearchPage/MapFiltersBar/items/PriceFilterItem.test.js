import React from 'react';
import '@testing-library/jest-dom';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import userEvent from '@testing-library/user-event';
import { mocked } from 'test-utils';
import PriceFilterItem from './PriceFilterItem';
import { getQueryPayWith, getQueryMinPrice, getQueryMaxPrice } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';
import * as config from 'config';

const PAYMENT_METHODS_POINTS = 'points';
jest.mock('config');
jest.mock('store/router/routerSelectors');
jest.mock('store/search/searchActions', () => ({
  updateQuery: jest.fn(),
}));

const mockedUpdateQuery = mocked(updateQuery);

jest.mock('./FilterItem', () => ({ children, buttonText, isHighlighted, clearFilters, ...rest }) => (
  <div data-testid="mock-filter-item" {...rest}>
    <button type="button" onClick={clearFilters} data-testid="filter-item-clear-button">
      {buttonText}
    </button>
    {isHighlighted && <span data-testid="highlight-indicator">Highlighted</span>}
    {children}
  </div>
));

jest.mock('components/Filters/Search/PriceFilter', () => ({ onChange }) => (
  <div data-testid="mock-price-filter">
    <input
      type="text"
      placeholder="Min Price"
      data-testid="min-price-input"
      onChange={(e) => onChange({ minPrice: e.target.value === '' ? undefined : Number(e.target.value) })}
    />
    <input
      type="text"
      placeholder="Max Price"
      data-testid="max-price-input"
      onChange={(e) => onChange({ maxPrice: e.target.value === '' ? undefined : Number(e.target.value) })}
    />
    <button type="button" data-testid="price-filter-apply-button" onClick={() => onChange({ somePriceFilterApplied: true })}>
      Apply Price Filter
    </button>
  </div>
));

jest.mock('components/Filters/Search/PayWith', () => ({ onChange }) => (
  <div data-testid="mock-pay-with">
    <label htmlFor="pay-with-points">
      <input
        type="checkbox"
        id="pay-with-points"
        data-testid="pay-with-points-checkbox"
        onChange={(e) => onChange(e.target.checked ? PAYMENT_METHODS_POINTS : undefined)}
      />
      Pay with Points
    </label>
  </div>
));

describe('PriceFilterItem', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    config.PAYWITH_TOGGLE_ENABLED = true;
    getQueryPayWith.mockReturnValue(undefined);
    getQueryMinPrice.mockReturnValue(undefined);
    getQueryMaxPrice.mockReturnValue(undefined);
  });

  it('renders with "Price" button text and is not highlighted by default', () => {
    const { getByRole, queryByTestId } = renderWithProviders(<PriceFilterItem />);
    const button = getByRole('button', { name: 'Price' });
    expect(button).toBeInTheDocument();
    expect(queryByTestId('highlight-indicator')).not.toBeInTheDocument();
  });

  it('renders as highlighted when payWith is points', () => {
    getQueryPayWith.mockReturnValue(PAYMENT_METHODS_POINTS);

    const { getByRole, getByTestId } = renderWithProviders(<PriceFilterItem />);
    const button = getByRole('button', { name: 'Price' });
    expect(button).toBeInTheDocument();
    expect(getByTestId('highlight-indicator')).toBeInTheDocument();
  });

  it('renders as highlighted when minPrice is set', () => {
    getQueryMinPrice.mockReturnValue(100);

    const { getByRole, getByTestId } = renderWithProviders(<PriceFilterItem />);
    const button = getByRole('button', { name: 'Price' });
    expect(button).toBeInTheDocument();
    expect(getByTestId('highlight-indicator')).toBeInTheDocument();
  });

  it('renders as highlighted when maxPrice is set', () => {
    getQueryMaxPrice.mockReturnValue(100);

    const { getByRole, getByTestId } = renderWithProviders(<PriceFilterItem />);
    const button = getByRole('button', { name: 'Price' });
    expect(button).toBeInTheDocument();
    expect(getByTestId('highlight-indicator')).toBeInTheDocument();
  });

  it('dispatches updateQuery action with expected query when clear filters is clicked', async () => {
    getQueryMinPrice.mockReturnValue(50);

    const { getByRole } = renderWithProviders(<PriceFilterItem />);
    const clearButton = getByRole('button', { name: 'Price' });

    await userEvent.click(clearButton);

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ minPrice: undefined, maxPrice: undefined });
  });

  it('dispatches updateQuery action when min price input changes in PriceFilter', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const minPriceInput = getByTestId('min-price-input');

    await userEvent.type(minPriceInput, '50');

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ minPrice: 50 });
  });

  it('dispatches updateQuery action when max price input changes in PriceFilter', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const maxPriceInput = getByTestId('max-price-input');

    await userEvent.type(maxPriceInput, '500');

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ maxPrice: 500 });
  });

  it('dispatches updateQuery action when "Apply Price Filter" button is clicked', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const applyButton = getByTestId('price-filter-apply-button');

    await userEvent.click(applyButton);

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ somePriceFilterApplied: true });
  });

  it('renders <PayWith /> when PAYWITH_TOGGLE_ENABLED is true', () => {
    config.PAYWITH_TOGGLE_ENABLED = true;
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    expect(getByTestId('mock-pay-with')).toBeInTheDocument();
  });

  it('does NOT render <PayWith /> when PAYWITH_TOGGLE_ENABLED is false', () => {
    config.PAYWITH_TOGGLE_ENABLED = false;
    const { queryByTestId } = renderWithProviders(<PriceFilterItem />);
    expect(queryByTestId('mock-pay-with')).not.toBeInTheDocument();
  });

  it('dispatches updateQuery action when PayWith "Pay with Points" checkbox is toggled', async () => {
    config.PAYWITH_TOGGLE_ENABLED = true;
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const payWithPointsCheckbox = getByTestId('pay-with-points-checkbox');

    await userEvent.click(payWithPointsCheckbox);
    expect(mockedUpdateQuery).toHaveBeenCalledWith(PAYMENT_METHODS_POINTS);
    mockedUpdateQuery.mockClear();

    await userEvent.click(payWithPointsCheckbox);
    expect(mockedUpdateQuery).toHaveBeenCalledWith(undefined);
  });

  it('handles keyboard navigation for "Price" button to clear filters', async () => {
    getQueryMinPrice.mockReturnValue(100);

    const { getByRole } = renderWithProviders(<PriceFilterItem />);
    const priceButton = getByRole('button', { name: 'Price' });

    await userEvent.tab();
    expect(priceButton).toHaveFocus();

    await userEvent.keyboard('{enter}');

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ minPrice: undefined, maxPrice: undefined });
  });

  it('handles keyboard navigation for PriceFilter inputs', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const minPriceInput = getByTestId('min-price-input');
    const applyButton = getByTestId('price-filter-apply-button');

    minPriceInput.focus();
    await userEvent.keyboard('150');
    expect(mockedUpdateQuery).toHaveBeenCalledWith({ minPrice: 150 });
    mockedUpdateQuery.mockClear();

    await userEvent.tab();
    await userEvent.tab();
    expect(applyButton).toHaveFocus();
    await userEvent.keyboard('{enter}');
    expect(mockedUpdateQuery).toHaveBeenCalledWith({ somePriceFilterApplied: true });
  });

  it('does not highlight when no price or payment method filters are active', () => {
    const { queryByTestId } = renderWithProviders(<PriceFilterItem />);
    expect(queryByTestId('highlight-indicator')).not.toBeInTheDocument();
  });

  it('dispatches updateQuery with NaN for minPrice when non-numeric input is provided', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const minPriceInput = getByTestId('min-price-input');

    await userEvent.type(minPriceInput, 'abc');
    expect(mockedUpdateQuery).toHaveBeenCalledWith({ minPrice: NaN });
  });

  it('dispatches updateQuery with NaN for maxPrice when non-numeric input is provided', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const maxPriceInput = getByTestId('max-price-input');

    await userEvent.type(maxPriceInput, 'xyz');
    expect(mockedUpdateQuery).toHaveBeenCalledWith({ maxPrice: NaN });
  });

  it('dispatches updateQuery with undefined for minPrice when input is cleared', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const minPriceInput = getByTestId('min-price-input');

    await userEvent.type(minPriceInput, '123');
    mockedUpdateQuery.mockClear();
    await userEvent.clear(minPriceInput);
    expect(mockedUpdateQuery).toHaveBeenCalledWith({ minPrice: undefined });
  });

  it('dispatches updateQuery with undefined for maxPrice when input is cleared', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const maxPriceInput = getByTestId('max-price-input');

    await userEvent.type(maxPriceInput, '456');
    mockedUpdateQuery.mockClear();
    await userEvent.clear(maxPriceInput);
    expect(mockedUpdateQuery).toHaveBeenCalledWith({ maxPrice: undefined });
  });

  it('dispatches updateQuery for each rapid min price input change', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const minPriceInput = getByTestId('min-price-input');

    await userEvent.type(minPriceInput, '1');
    await userEvent.type(minPriceInput, '2');
    await userEvent.type(minPriceInput, '3');

    expect(mockedUpdateQuery).toHaveBeenCalledTimes(3);
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(1, { minPrice: 1 });
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(2, { minPrice: 12 });
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(3, { minPrice: 123 });
  });

  it('dispatches updateQuery for each rapid max price input change', async () => {
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const maxPriceInput = getByTestId('max-price-input');

    await userEvent.type(maxPriceInput, '9');
    await userEvent.type(maxPriceInput, '8');
    await userEvent.type(maxPriceInput, '7');

    expect(mockedUpdateQuery).toHaveBeenCalledTimes(3);
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(1, { maxPrice: 9 });
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(2, { maxPrice: 98 });
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(3, { maxPrice: 987 });
  });

  it('dispatches updateQuery for multiple rapid toggles of Pay with Points checkbox', async () => {
    config.PAYWITH_TOGGLE_ENABLED = true;
    const { getByTestId } = renderWithProviders(<PriceFilterItem />);
    const payWithPointsCheckbox = getByTestId('pay-with-points-checkbox');

    await userEvent.click(payWithPointsCheckbox);
    await userEvent.click(payWithPointsCheckbox);
    await userEvent.click(payWithPointsCheckbox);

    expect(mockedUpdateQuery).toHaveBeenCalledTimes(3);
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(1, PAYMENT_METHODS_POINTS);
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(2, undefined);
    expect(mockedUpdateQuery).toHaveBeenNthCalledWith(3, PAYMENT_METHODS_POINTS);
  });
});
