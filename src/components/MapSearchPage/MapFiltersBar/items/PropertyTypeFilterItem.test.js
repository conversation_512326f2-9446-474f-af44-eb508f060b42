import React from 'react';
import '@testing-library/jest-dom';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import userEvent from '@testing-library/user-event';
import { mocked } from 'test-utils';
import PropertyTypeFilterItem from './PropertyTypeFilterItem';
import { getQueryPropertyTypes } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';

jest.mock('store/search/searchActions', () => ({
  updateQuery: jest.fn(),
}));
const mockedUpdateQuery = mocked(updateQuery);

jest.mock('store/router/routerSelectors');

jest.mock('./FilterItem', () => ({ children, buttonText, isHighlighted, clearFilters, ...rest }) => (
  <div data-testid="mock-filter-item" {...rest}>
    <button type="button" onClick={clearFilters} data-testid="filter-item-clear-button">
      {buttonText}
    </button>
    {isHighlighted && <span data-testid="highlight-indicator">Highlighted</span>}
    {children}
  </div>
));

jest.mock('components/Filters/Search/PropertyTypeFilter', () => ({ onChange }) => (
  <div data-testid="mock-property-type-filter">
    <label htmlFor="hotel-checkbox">
      <input
        type="checkbox"
        id="hotel-checkbox"
        data-testid="hotel-checkbox"
        onChange={(e) => onChange({ propertyTypes: e.target.checked ? ['hotel'] : undefined })}
      />
      Hotel
    </label>
    <label htmlFor="apartment-checkbox">
      <input
        type="checkbox"
        id="apartment-checkbox"
        data-testid="apartment-checkbox"
        onChange={(e) => onChange({ propertyTypes: e.target.checked ? ['apartment'] : undefined })}
      />
      Apartment
    </label>
    <button type="button" data-testid="apply-property-type-filter-button" onClick={() => onChange({ foo: 'bar' })}>
      Apply
    </button>
  </div>
));

describe('PropertyTypeFilterItem', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getQueryPropertyTypes.mockReturnValue([]);
  });

  it('renders with "Property Types" button text and is not highlighted by default', () => {
    const { getByRole, queryByTestId } = renderWithProviders(<PropertyTypeFilterItem />);
    const button = getByRole('button', { name: 'Property Types' });

    expect(button).toBeInTheDocument();
    expect(queryByTestId('highlight-indicator')).not.toBeInTheDocument();
  });

  it('renders as highlighted when there are property types selected', () => {
    getQueryPropertyTypes.mockReturnValue(['hotel']);

    const { getByRole, getByTestId } = renderWithProviders(<PropertyTypeFilterItem />);
    const button = getByRole('button', { name: 'Property Types' });

    expect(button).toBeInTheDocument();
    expect(getByTestId('highlight-indicator')).toBeInTheDocument();
  });

  it('dispatches updateQuery action with expected query when clear filters is clicked', async () => {
    const { getByTestId } = renderWithProviders(<PropertyTypeFilterItem />);
    const clearButton = getByTestId('filter-item-clear-button');

    await userEvent.click(clearButton);

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ propertyTypes: undefined });
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(1);
  });

  it('dispatches updateQuery action when a property type checkbox is toggled', async () => {
    const { getByTestId } = renderWithProviders(<PropertyTypeFilterItem />);
    const hotelCheckbox = getByTestId('hotel-checkbox');

    await userEvent.click(hotelCheckbox);

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ propertyTypes: ['hotel'] });
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(1);

    mockedUpdateQuery.mockClear();

    await userEvent.click(hotelCheckbox);

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ propertyTypes: undefined });
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(1);
  });

  it('dispatches updateQuery action when "Apply" button in PropertyTypeFilter is clicked', async () => {
    const { getByTestId } = renderWithProviders(<PropertyTypeFilterItem />);
    const applyButton = getByTestId('apply-property-type-filter-button');
    const expectedQuery = { foo: 'bar' };

    await userEvent.click(applyButton);

    expect(mockedUpdateQuery).toHaveBeenCalledWith(expectedQuery);
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard navigation for "Property Types" button to clear filters', async () => {
    const { getByRole } = renderWithProviders(<PropertyTypeFilterItem />);
    const propertyTypesButton = getByRole('button', { name: 'Property Types' });

    propertyTypesButton.focus();
    await userEvent.keyboard('{enter}');

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ propertyTypes: undefined });
  });

  it('ensures accessibility with appropriate roles and labels for checkboxes', () => {
    const { getByRole, getByLabelText } = renderWithProviders(<PropertyTypeFilterItem />);
    expect(getByRole('checkbox', { name: 'Hotel' })).toBeInTheDocument();
    expect(getByLabelText('Hotel')).toBeInTheDocument();

    expect(getByRole('checkbox', { name: 'Apartment' })).toBeInTheDocument();
    expect(getByLabelText('Apartment')).toBeInTheDocument();
  });
});
