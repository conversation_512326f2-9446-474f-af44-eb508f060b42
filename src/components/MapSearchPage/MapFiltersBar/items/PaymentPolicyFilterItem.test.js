import React from 'react';
import '@testing-library/jest-dom';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import userEvent from '@testing-library/user-event';
import { mocked } from 'test-utils';
import PaymentPolicyFilterItem from './PaymentPolicyFilterItem';
import { getQueryDepositPay, getQueryFreeCancellation, getQueryClassicRewards } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';

jest.mock('store/search/searchActions', () => ({
  updateQuery: jest.fn(),
}));

const mockedUpdateQuery = mocked(updateQuery);

jest.mock('store/router/routerSelectors');

jest.mock('./FilterItem', () => ({ children, buttonText, isHighlighted, clearFilters, ...rest }) => (
  <div data-testid="mock-filter-item" {...rest}>
    <button type="button" data-testid="filter-item-main-button">
      {buttonText}
    </button>
    {isHighlighted && <span data-testid="highlight-indicator">Highlighted</span>}
    {children}
    <button type="button" onClick={clearFilters} data-testid="filter-item-clear-button">
      Clear
    </button>
  </div>
));

describe('PaymentPolicyFilterItem', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getQueryDepositPay.mockReturnValue(false);
    getQueryFreeCancellation.mockReturnValue(false);
    getQueryClassicRewards.mockReturnValue(false);
  });

  it('renders with "Popular Filters" button text and is not highlighted by default', () => {
    const { getByRole, queryByTestId } = renderWithProviders(<PaymentPolicyFilterItem />);
    const button = getByRole('button', { name: 'Popular Filters' });
    expect(button).toBeInTheDocument();
    expect(queryByTestId('highlight-indicator')).not.toBeInTheDocument();
  });

  it('renders as highlighted when depositPay is true', () => {
    getQueryDepositPay.mockReturnValue(true);

    const { getByTestId } = renderWithProviders(<PaymentPolicyFilterItem />);
    expect(getByTestId('highlight-indicator')).toBeInTheDocument();
  });

  it('renders as highlighted when freeCancellation is true', () => {
    getQueryFreeCancellation.mockReturnValue(true);

    const { getByTestId } = renderWithProviders(<PaymentPolicyFilterItem />);
    expect(getByTestId('highlight-indicator')).toBeInTheDocument();
  });

  it('renders as highlighted when classicRewards is true', () => {
    getQueryClassicRewards.mockReturnValue(true);

    const { getByTestId } = renderWithProviders(<PaymentPolicyFilterItem />);
    expect(getByTestId('highlight-indicator')).toBeInTheDocument();
  });

  it('dispatches updateQuery action with expected query when clear filters is clicked', async () => {
    getQueryDepositPay.mockReturnValue(true);

    const { getByRole } = renderWithProviders(<PaymentPolicyFilterItem />);
    const clearButton = getByRole('button', { name: 'Clear' });

    await userEvent.click(clearButton);

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ depositPay: undefined, freeCancellation: undefined, classicRewards: undefined });
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard navigation for clearing filters', async () => {
    getQueryFreeCancellation.mockReturnValue(true);

    const { getByRole } = renderWithProviders(<PaymentPolicyFilterItem />);
    const clearButton = getByRole('button', { name: 'Clear' });

    clearButton.focus();
    await userEvent.keyboard('{enter}');

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ depositPay: undefined, freeCancellation: undefined, classicRewards: undefined });
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(1);
  });
});
