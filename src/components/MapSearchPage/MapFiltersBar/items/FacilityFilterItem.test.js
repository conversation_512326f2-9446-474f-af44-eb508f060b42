import React from 'react';
import '@testing-library/jest-dom';
import { renderWithProviders } from 'test-utils/reactUtils';
import userEvent from '@testing-library/user-event';
import FacilityFilterItem from './FacilityFilterItem';
import { getQueryFacilities } from 'store/router/routerSelectors';
import { updateQuery } from 'store/search/searchActions';
import { mocked } from 'test-utils';

jest.mock('store/router/routerSelectors');
jest.mock('store/search/searchActions', () => ({
  updateQuery: jest.fn(),
}));

const mockedUpdateQuery = mocked(updateQuery);

jest.mock('./FilterItem', () => ({ children, buttonText, isHighlighted, clearFilters }) => (
  <div data-testid="filter-item" data-button-text={buttonText} data-is-highlighted={isHighlighted}>
    <button type="button" onClick={clearFilters} data-testid="clear-filters-button">
      Clear
    </button>
    {children}
  </div>
));

jest.mock('components/Filters/Search/FacilityFilter', () => ({ onChange }) => (
  <input type="text" data-testid="facility-filter-input" onChange={(e) => onChange({ facilities: e.target.value })} />
));

describe('FacilityFilterItem', () => {
  beforeEach(() => {
    getQueryFacilities.mockReturnValue([]);
    mockedUpdateQuery.mockClear();
  });

  it('renders with the expected button text and highlights status with no facilities elected', () => {
    const { getByTestId } = renderWithProviders(<FacilityFilterItem />);
    const filterItem = getByTestId('filter-item');
    expect(filterItem).toHaveAttribute('data-button-text', 'Facilities');
    expect(filterItem).toHaveAttribute('data-is-highlighted', 'false');
  });

  it('renders as highlighted when there are facilities', () => {
    getQueryFacilities.mockReturnValue(['wifi']);
    const { getByTestId } = renderWithProviders(<FacilityFilterItem />);
    const filterItem = getByTestId('filter-item');
    expect(filterItem).toHaveAttribute('data-is-highlighted', 'true');
  });

  it('dispatches updateQuery action with expected query when clear filters is clicked', async () => {
    getQueryFacilities.mockReturnValue(['parking']);

    const { getByTestId } = renderWithProviders(<FacilityFilterItem />);
    const clearButton = getByTestId('clear-filters-button');
    await userEvent.click(clearButton);

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ facilities: undefined });
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(1);
  });

  it('dispatches updateQuery action with the new facility query when FacilityFilter changes', async () => {
    const { getByTestId } = renderWithProviders(<FacilityFilterItem />);
    const facilityInput = getByTestId('facility-filter-input');
    const newFacility = 'parking';

    await userEvent.type(facilityInput, newFacility);

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ facilities: newFacility });
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(newFacility.length);
  });

  it('handles keyboard navigation for clearing filters', async () => {
    getQueryFacilities.mockReturnValue(['gym']);
    const { getByTestId } = renderWithProviders(<FacilityFilterItem />);
    const clearButton = getByTestId('clear-filters-button');

    clearButton.focus();
    await userEvent.keyboard('{enter}');

    expect(mockedUpdateQuery).toHaveBeenCalledWith({ facilities: undefined });
    expect(mockedUpdateQuery).toHaveBeenCalledTimes(1);
  });
});
