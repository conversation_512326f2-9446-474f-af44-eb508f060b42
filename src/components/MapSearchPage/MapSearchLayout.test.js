import React from 'react';
import '@testing-library/jest-dom';
import MapSearchLayout from './MapSearchLayout';
import { DESKTOP_MAP_SEARCH_LIMIT, MOBILE_MAP_SEARCH_LIMIT } from 'config';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';

jest.mock('components/LegacyBrowserBoundary', () => ({ children }) => <div>{children}</div>);
jest.mock('./MapSearchHelmet', () => () => <div>MapSearchHelmet</div>);
jest.mock('./ResultsMap', () => () => <div>ResultsMap</div>);
jest.mock('components/SearchResultFetcher', () => ({ limit }) => <div>SearchResultFetcher - Limit: {limit}</div>);
jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useListSearchGa4Event');

describe('MapSearchLayout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useBreakpoints.mockReturnValue({
      isLessThanBreakpoint: () => true,
    });
  });

  it('renders MapSearchHelmet', () => {
    const { getByText } = renderWithProviders(<MapSearchLayout />);
    expect(getByText('MapSearchHelmet')).toBeInTheDocument();
  });

  it('renders ResultsMap', () => {
    const { getByText } = renderWithProviders(<MapSearchLayout />);
    expect(getByText('ResultsMap')).toBeInTheDocument();
  });

  describe('when on mobile or tablet', () => {
    it('renders SearchResultFetcher with the mobile search limit', () => {
      const { getByText } = renderWithProviders(<MapSearchLayout />);
      expect(getByText(`SearchResultFetcher - Limit: ${MOBILE_MAP_SEARCH_LIMIT}`)).toBeInTheDocument();
    });
  });

  describe('when on desktop', () => {
    beforeEach(() => {
      useBreakpoints.mockReturnValue({
        isLessThanBreakpoint: () => false,
      });
    });

    it('renders SearchResultFetcher with the desktop search limit', () => {
      const { getByText } = renderWithProviders(<MapSearchLayout />);
      expect(getByText(`SearchResultFetcher - Limit: ${DESKTOP_MAP_SEARCH_LIMIT}`)).toBeInTheDocument();
    });
  });
});
