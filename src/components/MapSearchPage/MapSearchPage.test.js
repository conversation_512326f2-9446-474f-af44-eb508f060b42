import React from 'react';
import mockReact from 'react';
import MapSearchPage from './MapSearchPage';
import { clearResults } from 'store/search/searchActions';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import { mocked } from 'test-utils';

const MockMapSearchLayout = jest.fn();

jest.mock('@loadable/component', () => {
  return (loadFn) => {
    const MockedComponent = mockReact.lazy(loadFn);
    return function LoadedComponent(props) {
      return (
        <mockReact.Suspense fallback={<div>Loading...</div>}>
          <MockedComponent {...props} />
        </mockReact.Suspense>
      );
    };
  };
});

jest.mock('./MapSearchLayout', () => MockMapSearchLayout);

jest.mock('store/search/searchActions', () => ({
  clearResults: jest.fn(),
}));
const mockedClearResults = mocked(clearResults);

describe('MapSearchPage', () => {
  beforeEach(() => {
    MockMapSearchLayout.mockClear();
    MockMapSearchLayout.mockImplementation(() => <div data-testid="map-search-layout">Mock Map Search Layout Rendered</div>);
  });

  it('renders MapSearchLayout passing all props', async () => {
    const testProps = { propA: 'valueA', propB: 123, someOtherProp: true };
    const { findByTestId } = renderWithProviders(<MapSearchPage {...testProps} />);

    const mapSearchLayout = await findByTestId('map-search-layout');
    expect(mapSearchLayout).toBeInTheDocument();

    expect(MockMapSearchLayout).toHaveBeenCalledWith(expect.objectContaining(testProps), expect.any(Object));
  });

  it('dispatches clear search results on page navigation', () => {
    MapSearchPage.onExitPage({ store: { dispatch: jest.fn() } });

    expect(mockedClearResults).toHaveBeenCalledTimes(1);
  });
});
