import React from 'react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { mocked } from 'test-utils';
import FixedPropertyCard from './FixedPropertyCard';
import { property as propertyFixture, searchResults as searchResultsFixture } from 'components/MapSearchPage/fixtures';
import { useDataLayer } from 'hooks/useDataLayer';
import { usePersonalisation } from 'hooks/usePersonalisation';
import { getResults } from 'store/search/searchSelectors';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';

jest.mock('components/PropertyCard', () => {
  return function MockPropertyCard(props) {
    const { onClick, id, inline } = props;
    return (
      <button type="button" data-testid="property-card" data-id={id} data-inline={inline} onClick={onClick}>
        Mock Property Card - {id}
      </button>
    );
  };
});

jest.mock('hooks/useDataLayer');
jest.mock('hooks/usePersonalisation');
jest.mock('store/search/searchSelectors');

const emitInteractionEvent = jest.fn();
const mockTrackMapClick = jest.fn();
const mockPersonalisationHookValue = {
  trackMapClick: mockTrackMapClick,
  trackHotelClick: jest.fn(),
  trackHotelImpression: jest.fn(),
};

describe('FixedPropertyCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
    mocked(usePersonalisation).mockReturnValue(mockPersonalisationHookValue);
    mocked(getResults).mockReturnValue(searchResultsFixture);
  });

  it('renders PropertyCard with correct content', () => {
    const { getByTestId } = renderWithProviders(<FixedPropertyCard {...propertyFixture} />);

    const propertyCard = getByTestId('property-card');

    expect(propertyCard).toHaveTextContent(`Mock Property Card - ${propertyFixture.id}`);
  });

  it('emits an event to the data layer when clicked', async () => {
    const user = userEvent.setup();
    const { getByTestId } = renderWithProviders(<FixedPropertyCard {...propertyFixture} />);

    const propertyCard = getByTestId('property-card');
    await user.click(propertyCard);

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Price Marker',
      value: 'Property Card Selected',
    });
  });

  it('sends click event to personalisation', async () => {
    const user = userEvent.setup();
    const { getByTestId } = renderWithProviders(<FixedPropertyCard {...propertyFixture} />);

    const propertyCard = getByTestId('property-card');
    await user.click(propertyCard);

    expect(mockTrackMapClick).toHaveBeenCalledWith(propertyFixture.id);
  });
});
