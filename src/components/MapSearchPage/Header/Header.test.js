import React from 'react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { mocked } from 'test-utils';
import Header from './Header';
import { useDataLayer } from 'hooks/useDataLayer';
import { getQueryString, getQueryLocation } from 'store/router/routerSelectors';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';

let mockIsDesktop = true;

jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

jest.mock('store/router/routerSelectors', () => ({
  getQueryString: jest.fn(),
  getQueryLocation: jest.fn(),
}));

jest.mock('@qga/roo-ui/components', () => ({
  Hide: ({ children, xs, sm, md, lg }) => {
    const hideOnMobile = xs || sm;
    const hideOnDesktop = md || lg;

    if (hideOnMobile && !mockIsDesktop) {
      return null;
    }
    if (hideOnDesktop && mockIsDesktop) {
      return null;
    }

    return <div data-testid="hide-wrapper">{children}</div>;
  },
  Icon: ({ name, color, size, mr, mt, title }) => (
    <svg data-testid="mock-icon" data-name={name} data-color={color} data-size={size} data-mr={mr} data-mt={mt} title={title}></svg>
  ),
  Heading: {
    h1: ({ children, mb }) => (
      <h1 data-testid="mock-heading-h1" data-mb={mb}>
        {children}
      </h1>
    ),
  },
  Flex: ({ children, ...rest }) => (
    <div data-testid="mock-flex" {...rest}>
      {children}
    </div>
  ),
}));

jest.mock('components/BackLink', () => ({ to, onClick, label, ...rest }) => (
  <a href={to} onClick={onClick} aria-label={label} data-testid="desktop-back-link" {...rest}>
    {label}
  </a>
));
jest.mock('components/AppLink', () => ({ to, onClick, children, ...rest }) => (
  <a href={to} onClick={onClick} data-testid="app-back-link" {...rest}>
    {children}
  </a>
));
jest.mock('components/MobileAppBoundary', () => ({ children }) => <div data-testid="mobile-app-boundary-mock">{children}</div>);

describe('<Header />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getQueryLocation.mockReturnValue('Hobart');
    getQueryString.mockReturnValue('?sometext=true');
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
    mockIsDesktop = true;
  });

  describe('Desktop/Tablet Viewport', () => {
    beforeEach(() => {
      mockIsDesktop = true;
    });

    it('renders a "Back to search results" link with the correct URL', () => {
      const { getByRole } = renderWithProviders(<Header />);
      const backLink = getByRole('link', { name: /back to search results/i });
      expect(backLink).toBeInTheDocument();
      expect(backLink).toHaveAttribute('href', '/search/list?sometext=true');
    });

    it('omits latitude/longitude parameters from the back to search results link URL', () => {
      getQueryString.mockReturnValue('?neLat=12&neLng=12&swLat=12&swLng=12&location=Hobart');
      const { getByRole } = renderWithProviders(<Header />);
      const backLink = getByRole('link', { name: /back to search results/i });
      expect(backLink).toHaveAttribute('href', '/search/list?location=Hobart');
    });

    it('emits an interaction event when the "Back to search results" link is clicked', async () => {
      const { getByRole } = renderWithProviders(<Header />);
      const backLink = getByRole('link', { name: /back to search results/i });

      backLink.addEventListener('click', (e) => e.preventDefault());

      await userEvent.click(backLink);
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Return Search Page',
        value: 'Link Clicked Hobart',
      });
    });

    it('does not render the mobile-specific back link', () => {
      const { queryByTestId } = renderWithProviders(<Header />);
      expect(queryByTestId('app-back-link')).not.toBeInTheDocument();
    });
  });

  describe('Mobile Viewport', () => {
    beforeEach(() => {
      mockIsDesktop = false;
    });

    it('renders the mobile-specific back link with the correct URL', () => {
      const { getByTestId, getByLabelText } = renderWithProviders(<Header />);
      const mobileBackLink = getByTestId('app-back-link');

      expect(mobileBackLink).toBeInTheDocument();
      expect(mobileBackLink).toHaveAttribute('href', '/search/list?sometext=true');
      expect(getByLabelText('Back to search results')).toBeInTheDocument();
      expect(getByTestId('mock-icon')).toHaveAttribute('data-name', 'chevronLeft');
    });

    it('omits latitude/longitude parameters from the mobile back link URL', () => {
      getQueryString.mockReturnValue('?neLat=12&neLng=12&swLat=12&swLng=12&location=Hobart');
      const { getByTestId } = renderWithProviders(<Header />);
      const mobileBackLink = getByTestId('app-back-link');
      expect(mobileBackLink).toHaveAttribute('href', '/search/list?location=Hobart');
    });

    it('emits an interaction event when the mobile back link is clicked', async () => {
      const { getByTestId } = renderWithProviders(<Header />);
      const mobileBackLink = getByTestId('app-back-link');

      mobileBackLink.addEventListener('click', (e) => e.preventDefault());

      await userEvent.click(mobileBackLink);
      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Return Search Page',
        value: 'Link Clicked Hobart',
      });
    });

    it('does not render the desktop back link', () => {
      const { queryByTestId } = renderWithProviders(<Header />);
      expect(queryByTestId('desktop-back-link')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility and User Experience (Both Viewports)', () => {
    it('ensures the desktop back link has an accessible name for screen readers', () => {
      mockIsDesktop = true;
      const { getByRole } = renderWithProviders(<Header />);
      expect(getByRole('link', { name: 'Back to search results' })).toBeInTheDocument();
    });

    it('ensures the mobile back link has an accessible name for screen readers', () => {
      mockIsDesktop = false;
      const { getByTestId, getByLabelText } = renderWithProviders(<Header />);
      expect(getByLabelText('Back to search results')).toBeInTheDocument();
      expect(getByTestId('mock-icon')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty query string correctly for desktop link', () => {
      mockIsDesktop = true;
      getQueryString.mockReturnValue('');
      const { getByRole } = renderWithProviders(<Header />);
      const backLink = getByRole('link', { name: /back to search results/i });
      expect(backLink).toHaveAttribute('href', '/search/list?');
    });

    it('handles empty query string correctly for mobile link', () => {
      mockIsDesktop = false;
      getQueryString.mockReturnValue('');
      const { getByTestId } = renderWithProviders(<Header />);
      const mobileBackLink = getByTestId('app-back-link');
      expect(mobileBackLink).toHaveAttribute('href', '/search/list?');
    });

    it('handles null location without breaking', () => {
      getQueryLocation.mockReturnValue(null);
      mockIsDesktop = true;
      const { getByRole } = renderWithProviders(<Header />);
      expect(getByRole('link', { name: /back to search results/i })).toBeInTheDocument();
      mockIsDesktop = false;
      const { getByTestId } = renderWithProviders(<Header />);
      expect(getByTestId('app-back-link')).toBeInTheDocument();
    });
  });
});
