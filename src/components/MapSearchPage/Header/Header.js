import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import qs from 'query-string';
import omit from 'lodash/omit';
import styled from '@emotion/styled';
import { Flex, Hide, Icon, Heading } from '@qga/roo-ui/components';
import { getQueryLocation, getQueryString } from 'store/router/routerSelectors';
import { HEADER_HEIGHT } from 'components/MapSearchPage/primitives';
import BackLink from 'components/BackLink';
import AppLink from 'components/AppLink';
import MobileAppBoundary from 'components/MobileAppBoundary';
import { themeGet } from 'styled-system';
import { mediaQuery } from 'lib/styledSystem/mediaQuery';
import { useDataLayer } from 'hooks/useDataLayer';

const StyledBackLink = styled(AppLink)`
  color: ${themeGet('colors.greys.steel')};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
`;

const HeaderWrapper = styled(Flex)`
  flex: 0 0 ${HEADER_HEIGHT};
  display: flex;
  align-items: center;
  border-bottom: 1px solid ${themeGet('colors.greys.alto')};
  background: ${themeGet('colors.white')};
  padding-left: ${themeGet('space.2')};
  padding-right: ${themeGet('space.2')};

  ${mediaQuery.minWidth.sm} {
    flex-basis: 54px;
  }
`;

const omittedProperties = ['neLat', 'neLng', 'swLat', 'swLng'];

const listSearchQueryString = (queryString) => {
  const updatedQuery = omit(qs.parse(queryString), omittedProperties);
  return `?${qs.stringify(updatedQuery)}`;
};

const Header = () => {
  const location = useSelector(getQueryLocation);
  const searchQueryString = useSelector(getQueryString);
  const { emitInteractionEvent } = useDataLayer();
  const updatedQueryString = listSearchQueryString(searchQueryString);

  const handleOnClick = useCallback(() => {
    emitInteractionEvent({ type: 'Return Search Page', value: `Link Clicked ${location}` });
  }, [emitInteractionEvent, location]);

  return (
    <HeaderWrapper>
      <Hide xs sm>
        <Heading.h1 mb={0}>
          <BackLink
            to={`/search/list${updatedQueryString}`}
            onClick={handleOnClick}
            label="Back to search results"
            data-testid="desktop-back-link"
          />
        </Heading.h1>
      </Hide>
      <Hide lg md>
        <MobileAppBoundary>
          <StyledBackLink
            to={`/search/list${updatedQueryString}`}
            onClick={handleOnClick}
            data-testid="app-back-link"
            aria-label="Back to search results"
          >
            <Icon name="chevronLeft" color="greys.charcoal" size={24} mr={2} mt={2} />
          </StyledBackLink>
        </MobileAppBoundary>
      </Hide>
    </HeaderWrapper>
  );
};

export default Header;
