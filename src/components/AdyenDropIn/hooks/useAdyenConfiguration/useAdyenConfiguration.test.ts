import { act, renderHook } from '@testing-library/react-hooks';
import { useSelector } from 'react-redux';
import { Decimal } from 'decimal.js';
import useAdyenConfiguration, { DEFAULT_PAYMENT_METHODS } from './useAdyenConfiguration';
import * as checkoutSelectors from 'store/checkout/checkoutSelectors';
import * as quoteSelectors from 'store/quote/quoteSelectors';

jest.mock('config', () => {
  const actual = jest.requireActual('config');
  return {
    ...actual,
    ADYEN_ENVIRONMENT: 'test',
    ADYEN_QEPG_CLIENT_KEY: 'adyen-client-key',
    IS_PRODUCTION: false,
  };
});

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));
const mockUseSelector = useSelector as jest.Mock;

beforeEach(() => {
  mockUseSelector.mockImplementation((selectorFn) => {
    if (selectorFn === checkoutSelectors.getPayableNowCashAmount) {
      return new Decimal(100.23);
    }
    if (selectorFn === checkoutSelectors.getPayableLaterCashAmount) {
      return new Decimal(2100.0);
    }
    if (selectorFn === quoteSelectors.getPayableLaterDueDate) {
      return new Date('2025-05-29');
    }
  });
});

afterEach(() => {
  jest.resetAllMocks();
});

test('returns useAdyenConfiguration default state', () => {
  const { result } = renderHook(() =>
    useAdyenConfiguration({
      paymentMethods: DEFAULT_PAYMENT_METHODS,
    }),
  );

  expect(result.current).toEqual({
    adyenConfiguration: {
      environment: 'test',
      clientKey: 'adyen-client-key',
      countryCode: 'AU',
      locale: 'en-US',
      showPayButton: false,
      analytics: {
        enabled: false,
      },
      paymentMethodsResponse: {
        paymentMethods: DEFAULT_PAYMENT_METHODS,
        storedPaymentMethods: [],
      },
      amount: {
        value: 100.23,
        currency: 'AUD',
      },
      translations: {
        'en-US': {
          'form.instruction': "You'll be charged $100.23 AUD now and $2,100.00 AUD on Thu 29 May, 2025",
          'creditCard.expiryDate.contextualText': 'MM/YY format',
          'creditCard.securityCode.contextualText.3digits': '3 digits CVC/CVV2',
          'creditCard.securityCode.contextualText.4digits': '4 digits CID',
          'paymentMethodsList.storedPayments.label': 'Saved payment methods',
          'paymentMethodsList.otherPayments.label': 'More payment options',
          storeDetails:
            'Save these card details for future bookings. I confirm I am the cardholder, or I have express permission from the cardholder to save and use this card. If the cardholder withdraws permission, I will remove the card from my membership profile',
        },
      },
      onEnterKeyPressed: expect.any(Function),
      onError: expect.any(Function),
    },
  });
});

test('returns default paymentMethods if not available', () => {
  const { result } = renderHook(() => useAdyenConfiguration());

  expect(result.current?.adyenConfiguration?.paymentMethodsResponse?.paymentMethods).toEqual(DEFAULT_PAYMENT_METHODS);
});

test('returns useAdyenConfiguration with error', () => {
  const { result } = renderHook(() => useAdyenConfiguration());

  expect(result.current.error).toBeUndefined();

  act(() => {
    result.current.adyenConfiguration?.onError({
      name: '',
      cause: '',
      options: {
        cause: 'Service Unavailable',
        code: '503',
      },
      message: 'Error message',
    });
  });

  expect(result.current.error).toEqual('Error message');
});
