import React from 'react';
import { screen } from '@testing-library/react';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import PropertySummary from './PropertySummaryFacilities';
import { getExclusiveOffer } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import merge from 'lodash/merge';
import { useBreakpoints } from 'hooks/useBreakpoints';

jest.mock('store/ui/uiSelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('hooks/useBreakpoints');
jest.mock('components/PropertyPage/ExclusiveOfferConditions', () => () => (
  <div data-testid="ExclusiveOfferConditions">ExclusiveOfferConditions</div>
));
jest.mock('components/PropertyPage/PropertyFacilities', () => ({ facilities, summary, interactionEventValue }) => (
  <div
    data-testid="PropertyFacilities"
    data-facilities={JSON.stringify(facilities)}
    data-summary={summary}
    data-interaction-event-value={interactionEventValue}
  >
    PropertyFacilities
  </div>
));
jest.mock('../OfferInclusions/OfferInclusions', () => ({ inclusions }) => (
  <div data-testid="OfferInclusions" data-inclusions={JSON.stringify(inclusions)}>
    OfferInclusions
  </div>
));

const menuItems = [
  {
    name: 'photos',
    text: 'Photos',
    id: 'photos',
    justifyContent: 'flex-start',
  },
  {
    name: 'rooms',
    text: 'Rooms',
    id: 'rooms',
    justifyContent: 'flex-start',
  },
  {
    name: 'highlights',
    text: 'Highlights',
    id: 'highlights',
    justifyContent: 'flex-start',
    isExclusive: true,
  },
  {
    name: 'offer-terms',
    text: 'Offer Terms',
    id: 'offer-terms',
    justifyContent: 'center',
    isExclusive: true,
  },
  {
    name: 'about-property',
    text: 'About this property',
    id: 'about-property',
    justifyContent: 'center',
  },
  {
    name: 'location',
    text: 'Location',
    id: 'location',
    justifyContent: 'flex-end',
  },
  {
    name: 'property-policies',
    text: 'Property policies',
    id: 'property-policies',
    justifyContent: 'flex-end',
  },
];

const exclusiveOfferOne = {
  highlights: {
    inclusions: [],
  },
  inclusionSets: [],
};

const exclusiveOfferTwo = merge(
  {
    highlights: {
      inclusions: [
        { code: 'check', name: 'Free parking' },
        { code: 'schedule', name: 'Free breakfast' },
        { code: 'place', name: 'Free bottle of wine' },
      ],
    },
  },
  exclusiveOfferOne,
);

const exclusiveOfferThree = merge(
  {
    inclusionSets: [
      {
        _type: 'common.inclusionSet',
        _key: '7279fe8bf346',
        title: 'Rabbit',
        inclusions: [
          {
            _id: '01c2c26b-4b34-4a08-b18f-b7a260770696',
            name: 'Daily Lunch',
            _type: 'common.inclusion',
            code: 'check',
          },
          {
            subtitle: 'Check-out will be 4pm',
            _type: 'common.inclusion',
            code: 'place',
            _id: '316e69cc-b44e-4370-b5a9-f8913b10da56',
            name: 'Later check-out',
          },
        ],
      },
      {
        _type: 'common.inclusionSet',
        _key: '7279fe8bfea6',
        title: 'Possum',
        inclusions: [
          {
            _id: '01c2c26b-4b34-4a08-b18f-b7a260770696',
            name: 'Daily breakfast',
            _type: 'common.inclusion',
            code: 'check',
          },
          {
            subtitle: 'Check-out will be 1pm',
            _type: 'common.inclusion',
            code: 'place',
            _id: '316e69cc-b44e-4370-b5a9-f8913b10da56',
            name: 'Late check-out',
          },
        ],
      },
      {
        _type: 'common.inclusionSet',
        _key: '808ca79b8194',
        title: 'Early Bird',
        inclusions: [
          {
            subtitle: 'Our high-speed wi-fi is available in all rooms',
            _type: 'common.inclusion',
            code: 'wifi',
            _id: '6ca93a1b-b799-445b-9b36-7774a53538fe',
            name: 'Free wi-fi',
          },
          {
            _type: 'common.inclusion',
            code: 'check',
            _id: '6559dc4c-843b-4e66-b4f7-90d3612e5e0a',
            name: 'Complimentary scheduled shuttle service',
          },
        ],
      },
      {
        _type: 'common.inclusionSet',
        _key: '5439fe8bf346',
        title: 'Turtle',
        inclusions: [
          {
            _id: '01c2c26b-4b34-4a08-b18f-b7a260770696',
            name: 'Daily Dinner',
            _type: 'common.inclusion',
            code: 'check',
          },
          {
            subtitle: 'Check-in will be 1pm',
            _type: 'common.inclusion',
            code: 'place',
            _id: '316e69cc-b44e-4370-b5a9-f8913b10da56',
            name: 'Early check-in',
          },
        ],
      },
    ],
  },
  exclusiveOfferTwo,
);

const exclusiveOfferWithoutInclusions = {
  highlights: {},
  inclusionSets: [],
};

const property = {
  address: {
    streetAddress: ['Plot 109, Malula Village', 'Moshi-Arusha Road'],
    suburb: 'Arusha',
    state: 'Arusha',
    postcode: '1234',
    country: 'Tanzania',
    countryCode: 'TZ',
  },
  name: 'Airport Planet Lodge',
  propertyFacilities: ['pool', 'parking'],
  latitude: 123,
  longitude: 456,
  renovations: 'renovations',
};

const defaultProps = { property, hasPropertyFacilities: true, menuItems };

const render = (props) => renderWithProviders(<PropertySummary {...defaultProps} {...props} />);

const expectOfferInclusions = (inclusions) => {
  const offerInclusions = screen.getByTestId('OfferInclusions');
  expect(offerInclusions).toBeInTheDocument();
  expect(offerInclusions).toHaveAttribute('data-inclusions', JSON.stringify(inclusions));
};

describe('<PropertyDescription />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getExclusiveOffer.mockReturnValue(null);
    useBreakpoints.mockReturnValue({
      isLessThanBreakpoint: jest.fn().mockReturnValue(true),
    });
  });

  describe('PropertyFacilities', () => {
    describe('when there are facilities', () => {
      it('renders the PropertyFacilities', () => {
        render();
        const propertyFacilities = screen.getByTestId('PropertyFacilities');
        expect(propertyFacilities).toHaveAttribute('data-facilities', JSON.stringify(property.propertyFacilities));
        expect(propertyFacilities).toHaveAttribute('data-summary', 'true');
        expect(propertyFacilities).toHaveAttribute('data-interaction-event-value', 'Top Link Selected');
      });

      it('renders the facilities heading', () => {
        render();
        expect(screen.getByTestId('facilities-heading')).toBeInTheDocument();
        expect(screen.getByText('Most popular facilities')).toBeInTheDocument();
      });

      it('renders PropertyFacilities with summary false on desktop', () => {
        useBreakpoints.mockReturnValue({
          isLessThanBreakpoint: jest.fn().mockReturnValue(false),
        });
        render();
        const propertyFacilities = screen.getByTestId('PropertyFacilities');
        expect(propertyFacilities).toHaveAttribute('data-summary', 'false');
      });
    });

    describe('when there are no facilities', () => {
      it('does NOT render the PropertyFacilities', () => {
        render({ hasPropertyFacilities: false });
        expect(screen.queryByTestId('PropertyFacilities')).not.toBeInTheDocument();
      });

      it('does NOT render the facilities heading', () => {
        render({ hasPropertyFacilities: false });
        expect(screen.queryByTestId('facilities-heading')).not.toBeInTheDocument();
      });
    });

    describe('when in exclusive offer mode', () => {
      beforeEach(() => {
        getExclusiveOffer.mockReturnValue(exclusiveOfferTwo);
      });

      it('does NOT render PropertyFacilities even when hasPropertyFacilities is true', () => {
        render();
        expect(screen.queryByTestId('PropertyFacilities')).not.toBeInTheDocument();
      });

      it('does NOT render the facilities heading', () => {
        render();
        expect(screen.queryByTestId('facilities-heading')).not.toBeInTheDocument();
      });
    });
  });

  it('does NOT render the ExclusiveOfferConditions', () => {
    render();
    expect(screen.queryByTestId('ExclusiveOfferConditions')).not.toBeInTheDocument();
  });

  describe('when in exclusive offer mode', () => {
    beforeEach(() => {
      getExclusiveOffer.mockReturnValue(exclusiveOfferThree);
    });

    it('renders the ExclusiveOfferConditions', () => {
      render();
      expect(screen.getByTestId('ExclusiveOfferConditions')).toBeInTheDocument();
    });

    it('renders the exclusive inclusions heading', () => {
      render();
      expect(screen.getByText('Exclusive inclusions:')).toBeInTheDocument();
    });

    describe('when there are exclusive inclusions', () => {
      describe('when there are inclusionsSets', () => {
        it('renders the default inclusions', () => {
          render();
          expectOfferInclusions(exclusiveOfferTwo.highlights.inclusions);
        });
      });

      describe('when there are no inclusionSets', () => {
        beforeEach(() => {
          getExclusiveOffer.mockReturnValue(exclusiveOfferTwo);
        });

        it('renders the OfferInclusions with the highlights inclusions', () => {
          render();
          expectOfferInclusions(exclusiveOfferTwo.highlights.inclusions);
        });
      });

      describe('when there are no inclusions at all', () => {
        beforeEach(() => {
          getExclusiveOffer.mockReturnValue(exclusiveOfferOne);
        });

        it('does not render OfferInclusions or OfferInclusionSetInclusions', () => {
          render();
          expect(screen.queryByTestId('OfferInclusions')).not.toBeInTheDocument();
          expect(screen.queryByTestId('OfferInclusionSetInclusions')).not.toBeInTheDocument();
        });
      });

      describe('when exclusive offer exists but highlights is undefined', () => {
        beforeEach(() => {
          getExclusiveOffer.mockReturnValue(exclusiveOfferWithoutInclusions);
        });

        it('does not render OfferInclusions', () => {
          render();
          expect(screen.queryByTestId('OfferInclusions')).not.toBeInTheDocument();
        });

        it('still renders the exclusive inclusions heading', () => {
          render();
          expect(screen.getByText('Exclusive inclusions:')).toBeInTheDocument();
        });
      });
    });
  });
});
