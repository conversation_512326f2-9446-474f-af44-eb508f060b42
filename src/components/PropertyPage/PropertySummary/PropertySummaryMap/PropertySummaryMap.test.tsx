import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import PropertySummaryMap from './PropertySummaryMap';
import { useBreakpoints } from 'hooks/useBreakpoints';

jest.mock('hooks/useBreakpoints', () => ({ useBreakpoints: jest.fn() }));

jest.mock('components/PropertyPage/PropertyMapStatic', () => ({ locationName, latitude, longitude, address, width, height }) => (
  <div
    data-testid="PropertyMapStatic"
    data-location-name={locationName}
    data-latitude={latitude}
    data-longitude={longitude}
    data-address={JSON.stringify(address)}
    data-width={JSON.stringify(width)}
    data-height={JSON.stringify(height)}
  />
));

jest.mock('components/PropertyPage/Renovations', () => ({ renovations }) => (
  <div data-testid="Renovations" data-renovations={renovations} />
));

interface Property {
  address: {
    streetAddress: string[];
    suburb: string;
    state: string;
    postcode: string;
    country: string;
    countryCode: string;
  };
  name: string;
  propertyFacilities: string[];
  latitude: number;
  longitude: number;
  renovations: string | undefined;
}

const property: Property = {
  address: {
    streetAddress: ['Plot 109, Malula Village', 'Moshi-Arusha Road'],
    suburb: 'Arusha',
    state: 'Arusha',
    postcode: '1234',
    country: 'Tanzania',
    countryCode: 'TZ',
  },
  name: 'Airport Planet Lodge',
  propertyFacilities: ['pool', 'parking'],
  latitude: 123,
  longitude: 456,
  renovations: 'renovations',
};

beforeEach(() => {
  jest.clearAllMocks();
  (useBreakpoints as jest.Mock).mockReturnValue({ isLessThanBreakpoint: () => false });
});

const renderComponent = (props = { property }) => renderWithProviders(<PropertySummaryMap {...props} />);

describe('PropertySummaryMap', () => {
  it('passes props to PropertyMapStatic', async () => {
    renderComponent();

    await waitFor(() => {
      const propertyMap = screen.getByTestId('PropertyMapStatic');
      expect(propertyMap).toHaveAttribute('data-location-name', property.name);
    });
    await waitFor(() => {
      const propertyMap = screen.getByTestId('PropertyMapStatic');
      expect(propertyMap).toHaveAttribute('data-latitude', property.latitude.toString());
    });
    await waitFor(() => {
      const propertyMap = screen.getByTestId('PropertyMapStatic');
      expect(propertyMap).toHaveAttribute('data-longitude', property.longitude.toString());
    });
    await waitFor(() => {
      const propertyMap = screen.getByTestId('PropertyMapStatic');
      expect(propertyMap).toHaveAttribute('data-address', JSON.stringify(property.address));
    });
    await waitFor(() => {
      const propertyMap = screen.getByTestId('PropertyMapStatic');
      expect(propertyMap).toHaveAttribute('data-width', JSON.stringify([640, 640, 400]));
    });
    await waitFor(() => {
      const propertyMap = screen.getByTestId('PropertyMapStatic');
      expect(propertyMap).toHaveAttribute('data-height', JSON.stringify([400, 640, 220]));
    });
  });

  describe('Renovations', () => {
    beforeEach(() => (useBreakpoints as jest.Mock).mockReturnValue({ isLessThanBreakpoint: () => true }));

    describe('with renovations', () => {
      it('renders the renovations text block', async () => {
        renderComponent();

        await waitFor(() => {
          const renovations = screen.getByTestId('Renovations');
          expect(renovations).toHaveAttribute('data-renovations', property.renovations);
        });
      });
    });

    describe('when renovations is undefined', () => {
      const propertyWithoutRenovations = {
        ...property,
        renovations: undefined,
      };

      it('does not render the renovations text block', async () => {
        renderComponent({ property: propertyWithoutRenovations });

        await waitFor(() => {
          expect(screen.queryByTestId('Renovations')).not.toBeInTheDocument();
        });
      });
    });

    describe('when renovations is "undefined" string', () => {
      const propertyWithUndefinedString = {
        ...property,
        renovations: 'undefined',
      };

      it('does not render the renovations text block', async () => {
        renderComponent({ property: propertyWithUndefinedString });

        await waitFor(() => {
          expect(screen.queryByTestId('Renovations')).not.toBeInTheDocument();
        });
      });
    });

    describe('when not mobile', () => {
      beforeEach(() => (useBreakpoints as jest.Mock).mockReturnValue({ isLessThanBreakpoint: () => false }));

      it('does not render the renovations text block even with renovations', async () => {
        renderComponent();

        await waitFor(() => {
          expect(screen.queryByTestId('Renovations')).not.toBeInTheDocument();
        });
      });
    });
  });
});
