/* eslint-disable prettier/prettier */

import React from 'react';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import PropertyHelmet from './PropertyHelmet';
import * as config from 'config';

jest.mock('config');
jest.mock('store/search/searchSelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');

const property = {
  id: '1',
  name: 'The Grand Budapest',
  description: 'This is a great property',
  rating: 4,
  ratingType: 'AAA',
  latitude: 'latitude',
  longitude: 'longitude',
  mainImage: {
    urlOriginal: 'urlOriginal',
    urlLarge: 'urlLarge',
  },
  address: {
    streetAddress: ['address line 1', 'address line 2'],
    suburb: 'suburb',
    state: 'state',
    country: 'country',
  },
  phone: '1300 738 206',
  images: [
    { urlOriginal: 'urlOriginal', caption: 'nice place' },
    { urlOriginal: 'urlOriginal', caption: 'entry' },
  ],
  customerRatings: [{ averageRating: 4, reviewCount: 100 }],
  propertyFacilities: ['wifi', 'sauna', 'indoor pool'],
};

const baseHotelSchema = {
  '@type': 'Hotel',
  name: 'The Grand Budapest',
  url: 'http://test/hotels/properties/1-the-grand-budapest',
  description: 'This is a great property',
  address: {
    '@type': 'PostalAddress',
    streetAddress: 'address line 1, address line 2',
    addressLocality: 'suburb',
    addressRegion: 'state',
    addressCountry: 'country',
  },
  contactPoint: { '@type': 'ContactPoint', telephone: '1300 738 206', contactType: 'customer service' },
  amenityFeature: ['wifi', 'sauna', 'indoor pool'],
};

const defaultAggregateRating = {
  '@type': 'AggregateRating',
  itemReviewed: { '@type': 'Thing', name: 'The Grand Budapest' },
  ratingValue: 4,
  reviewCount: 100,
};

const defaultStarRating = { '@type': 'Rating', ratingValue: 4, bestRating: 4 };
const defaultPhoto = [
  { url: 'urlOriginal', caption: 'nice place', '@type': 'ImageObject' },
  { url: 'urlOriginal', caption: 'entry', '@type': 'ImageObject' },
];

const createExpectedSchema = (hotelOverrides = {}, includeAggregateRating = true, aggregateRatingOverrides = {}) => ({
  '@context': 'https://schema.org',
  '@graph': [
    { ...baseHotelSchema, ...hotelOverrides },
    ...(includeAggregateRating ? [{ ...defaultAggregateRating, ...aggregateRatingOverrides }] : []),
  ],
});

const expectSchemaToMatch = (script, expectedSchema) => {
  const actualSchema = JSON.parse(script.textContent);
  expect(actualSchema).toEqual(expectedSchema);
};

const render = (props) => renderWithProviders(<PropertyHelmet property={property} {...props} />);

const expectDescriptionMeta = (expectedContent) => {
  expect(global.window.document.querySelector(`meta[name="description"]`)).toHaveAttribute('content', expectedContent);
};

beforeEach(() => {
  jest.clearAllMocks();
  config.HOTELS_BRAND_NAME = jest.requireActual('config').HOTELS_BRAND_NAME;
  config.HOTELS_URL = jest.requireActual('config').HOTELS_URL;
});

it('sets the correct page title with helmet', () => {
  render();
  expect(global.window.document.title).toBe(`The Grand Budapest | ${config.HOTELS_BRAND_NAME}`);
});

it('sets the correct canonical link on helmet', () => {
  render();
  const canonicalLink = `${config.HOTELS_URL}/properties/1-the-grand-budapest`;
  expect(global.window.document.querySelector('link[rel="canonical"]')).toHaveAttribute('href', canonicalLink);
});

test.each`
  name                      | content
  ${'hotels-booking-stage'} | ${'property-details'}
  ${'og:site_name'}         | ${config.HOTELS_BRAND_NAME}
  ${'og:title'}             | ${property.name}
  ${'og:url'}               | ${`${config.HOTELS_URL}/properties/1-the-grand-budapest`}
  ${'og:image'}             | ${property.mainImage.urlLarge}
  ${'og:latitude'}          | ${property.latitude}
  ${'og:longitude'}         | ${property.longitude}
  ${'og:street_address'}    | ${property.address.streetAddress.join(', ')}
  ${'og:locality'}          | ${property.address.suburb}
  ${'og:region'}            | ${property.address.state}
  ${'og:country_name'}      | ${property.address.country}
`('sets the meta for $name', ({ name, content }) => {
  render({ property });
  expect(global.window.document.querySelector(`meta[name="${name}"]`)).toHaveAttribute('content', content);
});

it('handles property with empty name', () => {
  const emptyNameProperty = { ...property, name: '' };
  render({ property: emptyNameProperty });
  expect(global.window.document.title).toBe(`| ${config.HOTELS_BRAND_NAME}`);
});

it('handles property with missing address fields', () => {
  const incompleteAddressProperty = {
    ...property,
    address: {
      streetAddress: [],
      suburb: '',
      state: '',
      country: '',
    },
  };
  render({ property: incompleteAddressProperty });
  const script = global.window.document.querySelector('script[type="application/ld+json"]');
  const expectedSchema = createExpectedSchema({
    starRating: defaultStarRating,
    image: 'urlOriginal',
    photo: defaultPhoto,
    address: {
      '@type': 'PostalAddress',
      streetAddress: '',
      addressLocality: '',
      addressRegion: '',
      addressCountry: '',
    },
  });
  expectSchemaToMatch(script, expectedSchema);
});

it('handles property with missing description', () => {
  const noDescriptionProperty = { ...property, description: '' };
  render({ property: noDescriptionProperty });
  const script = global.window.document.querySelector('script[type="application/ld+json"]');
  const actualSchema = JSON.parse(script.textContent);
  expect(actualSchema['@graph'][0].description).toBe('');
});

it('handles property with zero rating', () => {
  const zeroRatingProperty = { ...property, rating: 0 };
  render({ property: zeroRatingProperty });
  const script = global.window.document.querySelector('script[type="application/ld+json"]');
  const expectedSchema = createExpectedSchema({
    starRating: { '@type': 'Rating', ratingValue: 0, bestRating: 0 },
    image: 'urlOriginal',
    photo: defaultPhoto,
  });
  expectSchemaToMatch(script, expectedSchema);
});

it('handles property with no facilities', () => {
  const noFacilitiesProperty = { ...property, propertyFacilities: [] };
  render({ property: noFacilitiesProperty });
  const script = global.window.document.querySelector('script[type="application/ld+json"]');
  const expectedSchema = createExpectedSchema({
    starRating: defaultStarRating,
    image: 'urlOriginal',
    photo: defaultPhoto,
    amenityFeature: [],
  });
  expectSchemaToMatch(script, expectedSchema);
});

it('handles property with null facilities', () => {
  const nullFacilitiesProperty = { ...property, propertyFacilities: null };
  render({ property: nullFacilitiesProperty });
  const script = global.window.document.querySelector('script[type="application/ld+json"]');
  const expectedSchema = createExpectedSchema({
    starRating: defaultStarRating,
    image: 'urlOriginal',
    photo: defaultPhoto,
    amenityFeature: null,
  });
  expectSchemaToMatch(script, expectedSchema);
});

it('includes style tag for scroll padding', () => {
  render();
  const styleTag = global.window.document.querySelector('style[type="text/css"]');
  expect(styleTag).toHaveTextContent('html { scroll-padding-top: 50px; }');
});

describe('property structured schema', () => {
  it('sets the correct page markup for SEO', () => {
    render();
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    const expectedSchema = createExpectedSchema({
      starRating: defaultStarRating,
      image: 'urlOriginal',
      photo: defaultPhoto,
    });
    expectSchemaToMatch(script, expectedSchema);
  });

  it('only includes positive TripAdvisor review counts', () => {
    const selfRatedProperty = { ...property, customerRatings: [{ averageRating: 4, reviewCount: -10 }] };
    render({ property: selfRatedProperty });
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    const expectedSchema = createExpectedSchema(
      {
        starRating: defaultStarRating,
        image: 'urlOriginal',
        photo: defaultPhoto,
      },
      true,
      { reviewCount: null },
    );
    expectSchemaToMatch(script, expectedSchema);
  });

  it('does not include aggregateRating when there are no customer ratings', () => {
    const selfRatedProperty = { ...property, customerRatings: [] };
    render({ property: selfRatedProperty });
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    const expectedSchema = createExpectedSchema(
      {
        starRating: defaultStarRating,
        image: 'urlOriginal',
        photo: defaultPhoto,
      },
      false,
    );
    expectSchemaToMatch(script, expectedSchema);
  });

  it('does not include image when there is no main image', () => {
    const selfRatedProperty = { ...property, mainImage: null };
    render({ property: selfRatedProperty });
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    const expectedSchema = createExpectedSchema({
      starRating: defaultStarRating,
      image: '',
      photo: defaultPhoto,
    });
    expectSchemaToMatch(script, expectedSchema);
  });

  it('does not include photos when there are no images', () => {
    const selfRatedProperty = { ...property, images: null };
    render({ property: selfRatedProperty });
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    const expectedSchema = createExpectedSchema({
      starRating: defaultStarRating,
      image: 'urlOriginal',
      photo: [],
    });
    expectSchemaToMatch(script, expectedSchema);
  });

  it('does not return starRating in the markup when the ratingType is not AAA', () => {
    const selfRatedProperty = { ...property, ratingType: 'Self Rated' };
    render({ property: selfRatedProperty });
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    const expectedSchema = createExpectedSchema({
      starRating: null,
      image: 'urlOriginal',
      photo: defaultPhoto,
    });
    expectSchemaToMatch(script, expectedSchema);
  });

  it('handles images without captions', () => {
    const noCaptionProperty = {
      ...property,
      images: [{ urlOriginal: 'urlOriginal', caption: '' }, { urlOriginal: 'urlOriginal2' }],
    };
    render({ property: noCaptionProperty });
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    const expectedSchema = createExpectedSchema({
      starRating: defaultStarRating,
      image: 'urlOriginal',
      photo: [
        { url: 'urlOriginal', caption: '', '@type': 'ImageObject' },
        { url: 'urlOriginal2', caption: '', '@type': 'ImageObject' },
      ],
    });
    expectSchemaToMatch(script, expectedSchema);
  });

  it('handles multiple customer ratings', () => {
    const multipleRatingsProperty = {
      ...property,
      customerRatings: [
        { averageRating: 4, reviewCount: 100 },
        { averageRating: 3, reviewCount: 50 },
      ],
    };
    render({ property: multipleRatingsProperty });
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    const actualSchema = JSON.parse(script.textContent);
    expect(actualSchema['@graph'][1].ratingValue).toBe(4);
    expect(actualSchema['@graph'][1].reviewCount).toBe(100);
  });

  it('does not render script when schema is empty', () => {
    const emptyProperty = {
      ...property,
      name: '',
      description: '',
      customerRatings: [],
      images: null,
      mainImage: null,
      propertyFacilities: null,
    };
    render({ property: emptyProperty });
    const script = global.window.document.querySelector('script[type="application/ld+json"]');
    expect(script).toBeTruthy();
  });
});

describe('when POINTS_EARN_ENABLED is true', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = true;
  });

  it('renders the correct description', () => {
    render();
    expectDescriptionMeta(`Book Now & Earn Qantas Points on ${property.name}`);
  });
});

describe('when POINTS_EARN_ENABLED is false', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = false;
  });

  it('renders the correct description', () => {
    render();
    expectDescriptionMeta(`Book Your Stay at ${property.name} with ${config.HOTELS_BRAND_NAME}`);
  });
});

describe('when POINTS_EARN_ENABLED is undefined', () => {
  beforeEach(() => {
    config.POINTS_EARN_ENABLED = undefined;
  });

  it('renders the default description', () => {
    render();
    expectDescriptionMeta(`Book Your Stay at ${property.name} with ${config.HOTELS_BRAND_NAME}`);
  });
});
