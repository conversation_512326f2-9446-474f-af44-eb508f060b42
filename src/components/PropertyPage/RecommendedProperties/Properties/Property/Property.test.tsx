import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { mocked, createMockPropertyCardData, createMockPersonalisationEventHandlers } from 'test-utils';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import Property from './Property';
import { getPropertyLinkQueryString } from 'store/search/searchSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { registerInteraction } from 'lib/analytics/recommendations';
import { usePersonalisation } from 'hooks/usePersonalisation';

jest.mock('store/recommendedProperty/recommendedPropertySelectors');
jest.mock('lib/analytics/recommendations');
jest.mock('store/search/searchSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('hooks/usePersonalisation');
jest.mock('store/ui/uiSelectors');

jest.mock('components/PropertyCard', () => {
  return function MockPropertyCard(props) {
    return (
      <button
        data-testid="property-card"
        data-property-id={props.id}
        data-recommended={props.recommended}
        data-inline={props.inline}
        data-show-campaign-message={props.showCampaignMessage}
        data-featured-offer-id={props.featuredOfferId === undefined ? 'undefined' : props.featuredOfferId}
        onClick={props.onClick}
        type="button"
        style={{ background: 'none', border: 'none', padding: 0, margin: 0 }}
      >
        Property Card - {props.propertyName}
      </button>
    );
  };
});

const emitInteractionEvent = jest.fn();
const mockPerso = createMockPersonalisationEventHandlers();
const mockDeal = createMockPropertyCardData({ id: '123' });

const renderComponent = (props = {}) =>
  renderWithProviders(
    <Property
      {...mockDeal}
      index={0}
      checkIn={new Date('2024-01-01')}
      checkOut={new Date('2024-01-02')}
      offerId="test-offer-id"
      totalDiscount={{ amount: '0', currency: 'AUD' }}
      {...props}
    />,
  );

beforeEach(() => {
  jest.clearAllMocks();
  mocked(getPropertyLinkQueryString).mockReturnValue('propertyLink=queryString');
  mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  mocked(usePersonalisation).mockReturnValue(mockPerso);
});

it('renders the PropertyCard with the correct props', () => {
  renderComponent();

  const propertyCard = screen.getByTestId('property-card');
  expect(propertyCard).toHaveAttribute('data-property-id', '123');
  expect(propertyCard).toHaveAttribute('data-recommended', 'true');
  expect(propertyCard).toHaveAttribute('data-inline', 'true');
  expect(propertyCard).toHaveAttribute('data-show-campaign-message', 'true');
  expect(propertyCard).toHaveAttribute('data-featured-offer-id', 'undefined');
});

it('passes all props except index to PropertyCard', () => {
  const customProps = {
    propertyName: 'Custom Hotel',
    total: { amount: '500.00', currency: 'AUD' },
    rating: 4,
  };

  renderComponent(customProps);

  expect(screen.getByText('Property Card - Custom Hotel')).toBeInTheDocument();
});

it('maps id prop to propertyId in PropertyCard', () => {
  renderComponent({ id: 'custom-id' });

  expect(screen.getByTestId('property-card')).toHaveAttribute('data-property-id', 'custom-id');
});

describe('when clicking the property card', () => {
  const setupAndClick = async (props = {}) => {
    renderComponent(props);
    await userEvent.click(screen.getByTestId('property-card'));
  };

  it('emits an event to the data layer', async () => {
    await setupAndClick();

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Recommended Properties',
      value: 'Card 1 Selected',
      customAttributes: {
        user_event_value: '123',
      },
    });
  });

  it('emits correct card number for different index values', async () => {
    await setupAndClick({ index: 2 });

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Recommended Properties',
      value: 'Card 3 Selected',
      customAttributes: {
        user_event_value: '123',
      },
    });
  });

  it('uses correct property ID in analytics', async () => {
    await setupAndClick({ id: 'different-property-id' });

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Recommended Properties',
      value: 'Card 1 Selected',
      customAttributes: {
        user_event_value: 'different-property-id',
      },
    });
  });

  it('registers interaction with perso', async () => {
    await setupAndClick();

    expect(mockPerso.trackRecommendationClick).toHaveBeenCalledWith('123');
  });

  it('registers interaction with perso using correct property ID', async () => {
    await setupAndClick({ id: 'another-property-id' });

    expect(mockPerso.trackRecommendationClick).toHaveBeenCalledWith('another-property-id');
  });

  it('adds interaction to store', async () => {
    await setupAndClick();

    expect(registerInteraction).toHaveBeenCalledWith({
      placement: 'property_page',
      propertyId: '123',
      treatment: 'related_properties',
    });
  });

  it('adds interaction to store with correct property ID', async () => {
    await setupAndClick({ id: 'store-property-id' });

    expect(registerInteraction).toHaveBeenCalledWith({
      placement: 'property_page',
      propertyId: 'store-property-id',
      treatment: 'related_properties',
    });
  });
});

describe('analytics integration', () => {
  it('calls all three analytics functions on click', async () => {
    renderComponent();

    await userEvent.click(screen.getByTestId('property-card'));

    expect(emitInteractionEvent).toHaveBeenCalledTimes(1);
    expect(mockPerso.trackRecommendationClick).toHaveBeenCalledTimes(1);
    expect(registerInteraction).toHaveBeenCalledTimes(1);
  });

  it('handles zero index correctly', async () => {
    renderComponent({ index: 0 });

    await userEvent.click(screen.getByTestId('property-card'));

    expect(emitInteractionEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        value: 'Card 1 Selected',
      }),
    );
  });
});
