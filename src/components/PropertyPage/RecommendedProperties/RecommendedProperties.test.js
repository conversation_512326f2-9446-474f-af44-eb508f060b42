import React from 'react';
import { screen } from '@testing-library/react';
import { useInView } from 'react-intersection-observer';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import RecommendedProperties from './RecommendedProperties';
import { getIsDomestic, getPropertyLocation } from 'store/property/propertySelectors';
import { getGa4Properties } from 'store/recommendedProperty/recommendedPropertySelectors';
import { getSearchQuery } from 'store/search/searchSelectors';
import { emitRecommendedPropertiesResult } from 'store/recommendedProperty/recommendedPropertyActions';
import { getPointsConversion } from 'store/pointsBurnTiers/pointsBurnSelectors';

jest.mock('react-intersection-observer');
jest.mock('store/property/propertySelectors');
jest.mock('store/search/searchSelectors');
jest.mock('store/recommendedProperty/recommendedPropertySelectors');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');

jest.mock('./ResultsFetcher', () => {
  const ResultsFetcher = () => <div data-testid="results-fetcher">ResultsFetcher</div>;
  ResultsFetcher.displayName = 'ResultsFetcher';
  return ResultsFetcher;
});

jest.mock('./Properties/Properties', () => {
  const Properties = () => <div data-testid="properties">Properties</div>;
  Properties.displayName = 'Properties';
  return Properties;
});

const mockRef = React.createRef();

const regionName = 'Melbourne, VIC, Australia';
const payWith = { payWith: 'cash' };
const query = { location: regionName, payWith: 'cash' };

const propertyResults = {
  property: { id: '124', name: 'hotel' },
  roomType: { name: 'room' },
  offer: {
    charges: {
      total: {
        amount: '300.00',
        currency: 'AUD',
      },
      totalCash: {
        amount: '0',
        currency: 'AUD',
      },
    },
  },
};

const pointsConversion = {
  levels: [
    { min: 0, max: 150, rate: 0.00824 },
    { min: 150, max: 400, rate: 0.00834 },
    { min: 400, max: 650, rate: 0.00848 },
    { min: 650, max: 900, rate: 0.00875 },
    { min: 900, max: null, rate: 0.00931 },
  ],
  name: 'VERSION11',
};

const payload = {
  results: propertyResults,
  query,
  listName: 'Property Page Recommended Properties',
  category: 'qantas',
  type: 'list',
  currency: 'AUD',
  pointsConversion,
};

const render = () => {
  const { store, ...rest } = renderWithProviders(<RecommendedProperties />);
  return { store, ...rest };
};

const expectComponentsNotToRender = () => {
  expect(screen.queryByTestId('results-fetcher')).not.toBeInTheDocument();
  expect(screen.queryByTestId('properties')).not.toBeInTheDocument();
};

const expectComponentsToRender = () => {
  expect(screen.getByTestId('results-fetcher')).toBeInTheDocument();
  expect(screen.getByTestId('properties')).toBeInTheDocument();
};

beforeEach(() => {
  jest.clearAllMocks();
  getGa4Properties.mockReturnValue(propertyResults);
  getSearchQuery.mockReturnValue(payWith);
  getPropertyLocation.mockReturnValue(regionName);
  getPointsConversion.mockReturnValue(pointsConversion);
  getIsDomestic.mockReturnValue(true);
  useInView.mockReturnValue([mockRef, true]);
});

describe('RecommendedProperties', () => {
  it('configures useInView with triggerOnce: true', () => {
    render();
    expect(useInView).toHaveBeenCalledWith({ triggerOnce: true });
  });

  describe('when the property is domestic', () => {
    beforeEach(() => {
      getIsDomestic.mockReturnValue(true);
    });

    describe('and the component is in view', () => {
      beforeEach(() => {
        useInView.mockReturnValue([mockRef, true]);
      });

      it('renders both ResultsFetcher and Properties components', () => {
        render();
        expectComponentsToRender();
      });

      it('emits emitRecommendedPropertiesResult', () => {
        const { store } = render();
        expect(store.dispatch).toHaveBeenCalledWith(emitRecommendedPropertiesResult(payload));
      });

      describe('when propertyResults is empty', () => {
        beforeEach(() => {
          getGa4Properties.mockReturnValue({});
        });

        it('does not emit emitRecommendedPropertiesResult', () => {
          const { store } = render();
          expect(store.dispatch).not.toHaveBeenCalled();
        });

        it('still renders components', () => {
          render();
          expectComponentsToRender();
        });
      });

      describe('when pointsConversion has no levels', () => {
        beforeEach(() => {
          getPointsConversion.mockReturnValue({ name: 'VERSION11', levels: [] });
        });

        it('does not emit emitRecommendedPropertiesResult', () => {
          const { store } = render();
          expect(store.dispatch).not.toHaveBeenCalled();
        });

        it('still renders components', () => {
          render();
          expectComponentsToRender();
        });
      });

      describe('when pointsConversion is null', () => {
        beforeEach(() => {
          getPointsConversion.mockReturnValue(null);
        });

        it('does not emit emitRecommendedPropertiesResult', () => {
          const { store } = render();
          expect(store.dispatch).not.toHaveBeenCalled();
        });
      });

      describe('when searchQuery is null', () => {
        beforeEach(() => {
          getSearchQuery.mockReturnValue(null);
        });

        it('emits emitRecommendedPropertiesResult with undefined payWith', () => {
          const { store } = render();
          const expectedPayload = {
            ...payload,
            query: { location: regionName, payWith: undefined },
          };
          expect(store.dispatch).toHaveBeenCalledWith(emitRecommendedPropertiesResult(expectedPayload));
        });
      });

      describe('when searchQuery is undefined', () => {
        beforeEach(() => {
          getSearchQuery.mockReturnValue(undefined);
        });

        it('emits emitRecommendedPropertiesResult with undefined payWith', () => {
          const { store } = render();
          const expectedPayload = {
            ...payload,
            query: { location: regionName, payWith: undefined },
          };
          expect(store.dispatch).toHaveBeenCalledWith(emitRecommendedPropertiesResult(expectedPayload));
        });
      });

      describe('when regionName changes', () => {
        it('emits emitRecommendedPropertiesResult with updated location', () => {
          getPropertyLocation.mockReturnValue('Sydney, NSW, Australia');
          const { store } = render();
          const expectedPayload = {
            ...payload,
            query: { location: 'Sydney, NSW, Australia', payWith: 'cash' },
          };
          expect(store.dispatch).toHaveBeenCalledWith(emitRecommendedPropertiesResult(expectedPayload));
        });
      });
    });

    describe('and the component is out of view', () => {
      beforeEach(() => {
        useInView.mockReturnValue([mockRef, false]);
      });

      it('does not render ResultsFetcher or Properties components', () => {
        render();
        expectComponentsNotToRender();
      });

      it('does not emit emitRecommendedPropertiesResult', () => {
        const { store } = render();
        expect(store.dispatch).toHaveBeenCalledWith(emitRecommendedPropertiesResult(payload));
      });
    });
  });

  describe('when the property is international', () => {
    beforeEach(() => {
      getIsDomestic.mockReturnValue(false);
    });

    it('does not render any components regardless of view state', () => {
      useInView.mockReturnValue([mockRef, true]);
      render();
      expectComponentsNotToRender();

      useInView.mockReturnValue([mockRef, false]);
      render();
      expectComponentsNotToRender();
    });

    it('still emits emitRecommendedPropertiesResult when in view', () => {
      useInView.mockReturnValue([mockRef, true]);
      const { store } = render();
      expect(store.dispatch).toHaveBeenCalledWith(emitRecommendedPropertiesResult(payload));
    });
  });
});
