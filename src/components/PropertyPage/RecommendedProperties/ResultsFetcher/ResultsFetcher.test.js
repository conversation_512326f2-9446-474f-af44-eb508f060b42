import React from 'react';
import { waitFor, screen } from '@testing-library/react';
import {
  clearRecommendedPropertyAvailability,
  fetchRecommendedPropertyAvailability,
} from 'store/recommendedProperty/recommendedPropertyActions';
import { getFullKnownQuery } from 'store/router/routerSelectors';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import ResultsFetcher from './ResultsFetcher';

jest.mock('store/router/routerSelectors');

beforeEach(() => {
  jest.clearAllMocks();
  getFullKnownQuery.mockReturnValue({
    payWith: 'points',
    checkIn: new Date('2021-01-01'),
    checkOut: new Date('2021-01-03'),
  });
});

describe('on mount', () => {
  it('calls fetchRecommendedPropertyAvailability', async () => {
    const { store } = renderWithProviders(<ResultsFetcher />);

    await waitFor(() => {
      expect(store.dispatch).toHaveBeenCalledWith(fetchRecommendedPropertyAvailability());
    });
  });

  it('renders nothing', () => {
    renderWithProviders(<ResultsFetcher />);
    expect(screen.queryByTestId(/./)).toBeNull();
  });
});

describe('when the query updates', () => {
  it('calls fetchRecommendedPropertyAvailability', async () => {
    const { store } = renderWithProviders(<ResultsFetcher />);
    store.dispatch.mockClear();

    getFullKnownQuery.mockReturnValue({ payWith: 'cash' });
    const { store: newStore } = renderWithProviders(<ResultsFetcher />);

    await waitFor(() => {
      expect(newStore.dispatch).toHaveBeenCalledWith(fetchRecommendedPropertyAvailability());
    });
  });

  it('calls fetchRecommendedPropertyAvailability when query values change', async () => {
    const { store } = renderWithProviders(<ResultsFetcher />);
    store.dispatch.mockClear();

    getFullKnownQuery.mockReturnValue({
      payWith: 'points',
      checkIn: new Date('2021-02-01'),
      checkOut: new Date('2021-02-03'),
    });
    const { store: newStore } = renderWithProviders(<ResultsFetcher />);

    await waitFor(() => {
      expect(newStore.dispatch).toHaveBeenCalledWith(fetchRecommendedPropertyAvailability());
    });
  });

  it('calls fetchRecommendedPropertyAvailability when query becomes undefined', async () => {
    const { store } = renderWithProviders(<ResultsFetcher />);
    store.dispatch.mockClear();

    getFullKnownQuery.mockReturnValue(undefined);
    const { store: newStore } = renderWithProviders(<ResultsFetcher />);

    await waitFor(() => {
      expect(newStore.dispatch).toHaveBeenCalledWith(fetchRecommendedPropertyAvailability());
    });
  });
});

describe('on unmount', () => {
  it('calls clearRecommendedPropertyAvailability', async () => {
    const { store, unmount } = renderWithProviders(<ResultsFetcher />);

    unmount();

    await waitFor(() => {
      expect(store.dispatch).toHaveBeenCalledWith(clearRecommendedPropertyAvailability());
    });
  });

  it('does not call fetchRecommendedPropertyAvailability on unmount', async () => {
    const { store, unmount } = renderWithProviders(<ResultsFetcher />);
    store.dispatch.mockClear();

    unmount();

    await waitFor(() => {
      expect(store.dispatch).toHaveBeenCalledWith(clearRecommendedPropertyAvailability());
    });

    expect(store.dispatch).not.toHaveBeenCalledWith(fetchRecommendedPropertyAvailability());
  });
});

describe('dispatch dependency', () => {
  it('calls fetchRecommendedPropertyAvailability only once on initial render', async () => {
    const { store } = renderWithProviders(<ResultsFetcher />);

    await waitFor(() => {
      expect(store.dispatch).toHaveBeenCalledTimes(1);
    });
    await waitFor(() => {
      expect(store.dispatch).toHaveBeenCalledWith(fetchRecommendedPropertyAvailability());
    });
  });
});
