import React from 'react';
import { screen } from '@testing-library/react';
import PropertyMapStatic from './PropertyMapStatic';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import GoogleMapStaticImage from 'components/maps/GoogleMapStaticImage';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import formatPropertyAddress from 'components/formatters/FormatPropertyAddress';

jest.mock('components/maps/GoogleMapStaticImage', () => jest.fn().mockReturnValue(null));
jest.mock('components/PropertyPage/MapWithMarkerButton', () => ({ children, latitude, longitude, locationName, ...props }) => (
  <div
    data-testid="map-with-marker-button"
    data-latitude={latitude}
    data-longitude={longitude}
    data-location-name={locationName}
    {...props}
  >
    {children}
  </div>
));
jest.mock('store/userEnvironment/userEnvironmentSelectors');
jest.mock('components/formatters/FormatPropertyAddress');

const latitude = 123;
const longitude = 456;
const locationName = 'Property name';
const address = {
  suburb: 'Melbourne',
  state: 'Victoria',
  streetAddress: ['1 Parliament Square off Parliament Place'],
  country: 'Australia',
};

const defaultProps = {
  latitude,
  longitude,
  locationName,
  address,
};

const expectedFormatAddressParams = {
  streetAddressLines: address.streetAddress,
  suburb: address.suburb,
  state: address.state,
  country: address.country,
};

const getExpectedGoogleMapProps = (width = 400, height = 216) => ({
  location: `${latitude},${longitude}`,
  locationName,
  width,
  height,
  markers: true,
});

const render = (props = {}, initialState = {}) => renderWithProviders(<PropertyMapStatic {...defaultProps} {...props} />, { initialState });

describe('<PropertyMapStatic />', () => {
  beforeEach(() => {
    getBrowser.mockReturnValue({ name: 'Firefox' });
    formatPropertyAddress.mockReturnValue('1 Parliament Square off Parliament Place, Melbourne Victoria Australia');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the MapWithMarkerButton', () => {
    render();
    const mapButton = screen.getByTestId('map-with-marker-button');
    expect(mapButton).toBeInTheDocument();
    expect(mapButton).toHaveAttribute('data-latitude', String(latitude));
    expect(mapButton).toHaveAttribute('data-longitude', String(longitude));
    expect(mapButton).toHaveAttribute('data-location-name', locationName);
  });

  it('renders a static image without a button in IE', () => {
    getBrowser.mockReturnValue({ name: 'Internet Explorer' });
    render();
    expect(screen.queryByTestId('map-with-marker-button')).not.toBeInTheDocument();
  });

  it('renders the GoogleMapStaticImage as a child of MapWithMarkerButton', () => {
    render();
    expect(GoogleMapStaticImage).toHaveBeenCalledWith(getExpectedGoogleMapProps(), {});
  });

  it('renders the GoogleMapStaticImage with custom width and height', () => {
    const customWidth = 500;
    const customHeight = 300;
    render({ width: customWidth, height: customHeight });
    expect(GoogleMapStaticImage).toHaveBeenCalledWith(getExpectedGoogleMapProps(customWidth, customHeight), {});
  });

  it('renders the GoogleMapStaticImage in IE without MapWithMarkerButton', () => {
    getBrowser.mockReturnValue({ name: 'Internet Explorer' });
    render();
    expect(GoogleMapStaticImage).toHaveBeenCalledWith(getExpectedGoogleMapProps(), {});
  });

  it('displays the formatted property address', () => {
    render();
    expect(formatPropertyAddress).toHaveBeenCalledWith(expectedFormatAddressParams);
    expect(screen.getByText('1 Parliament Square off Parliament Place, Melbourne Victoria Australia')).toBeInTheDocument();
  });

  it('shows "View map" text when not in legacy browser', () => {
    render();
    expect(screen.getByText('View map')).toBeInTheDocument();
  });

  it('does not show "View map" text in IE', () => {
    getBrowser.mockReturnValue({ name: 'Internet Explorer' });
    render();
    expect(screen.queryByText('View map')).not.toBeInTheDocument();
  });
});
