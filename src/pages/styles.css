/*
  This is required to hide the recaptcha badge
  https://developers.google.com/recaptcha/docs/faq#id-like-to-hide-the-recaptcha-badge.-what-is-allowed
*/
.grecaptcha-badge {
  visibility: hidden;
}

/* This is required to style the Adyen fields. */
.js-iframe {
  border: none;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

:root {
  --adyen-sdk-color-label-primary: #323232;
  --adyen-sdk-color-background-secondary: #ffffff;
  --adyen-sdk-color-outline-primary-active: #8de2e0;
  --adyen-sdk-color-outline-tertiary: #dbdee2;
  --adyen-sdk-color-outline-primary: #d9d9d9;
  --adyen-sdk-color-outline-critical: #ed710b;
  --adyen-custom-color-text: #666666;
  --adyen-custom-color-background-text: #fcebcd;

  --adyen-sdk-text-body-font-size: 1.125rem;
  --adyen-sdk-text-body-font-weight: 600;
  --adyen-sdk-text-caption-font-size: 1rem;

  --adyen-sdk-spacer-110: 55px;
  --adyen-sdk-border-width-s: 2px;
  --adyen-sdk-border-radius-m: 0px;
}

@keyframes pulseBlock {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.adyen-checkout__dropin {
  margin-bottom: 1rem;

  &.adyen-checkout__dropin--ready {
    gap: 0px;
  }

  .adyen-checkout-payment-methods-container {
    padding: 1rem 1rem 0 1rem;
  }

  .adyen-checkout-payment-methods-container:nth-of-type(1) {
    .adyen-checkout__payment-method.adyen-checkout__payment-method--selected.adyen-checkout__payment-method--standalone {
      &:has(.adyen-checkout__payment-method__header__title:focus-visible) {
        border-radius: 0;
        outline-offset: 1rem;
      }
    }
  }

  .adyen-checkout-payment-methods-container:nth-of-type(2) {
    .adyen-checkout__payment-methods-list:has(.adyen-checkout__payment-method--selected) {
      border-top: 1px solid;
      border-color: var(--adyen-sdk-color-outline-primary);
      padding-top: 1rem;
      padding-left: 0;
    }

    .adyen-checkout-payment-methods-list-label {
      padding-top: 1rem;
    }

    .adyen-checkout__payment-method.adyen-checkout__payment-method--selected.adyen-checkout__payment-method--standalone {
      &:has(.adyen-checkout__payment-method__header__title:focus-visible) {
        border-radius: 0;
        outline-offset: 1rem;
      }
    }

    .adyen-checkout__payment-method--credit {
      &.adyen-checkout__payment-method--selected {
        &.adyen-checkout__payment-method--standalone {
          border: none;
          padding: 0rem;

          .adyen-checkout__payment-method__header {
            padding: 0;
          }

          .adyen-checkout__payment-method__details__content {
            padding: 0;
          }
        }
      }
    }
  }

  .adyen-checkout__payment-method {
    background: none;
    border: none;

    .adyen-checkout__spinner {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 1rem;
    }

    &:has(.adyen-checkout__payment-method__header__title:focus-visible) {
      outline: 1px solid var(--adyen-sdk-color-outline-primary-active);
    }

    &.adyen-checkout__payment-method--credit {
      border: 1px solid;
      border-radius: 8px;
      border-color: var(--adyen-sdk-color-outline-primary);
      padding: 0.3rem;

      .adyen-checkout__spinner:before,
      .adyen-checkout__spinner:after {
        position: static;
        animation: pulseBlock 2s ease-in-out infinite;
        background-color: var(--adyen-sdk-color-outline-primary);
        border: none;
        border-radius: 0;
        height: 59px;
        width: 49%;
      }

      &:hover {
        outline: none;
        border-color: var(--adyen-sdk-color-outline-primary-active);
        box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 8px 0px;
      }

      .adyen-checkout__payment-method__header__title:focus {
        outline-width: 0px;
        outline-offset: 0px;
      }

      .adyen-checkout__payment-method__header__title:focus-visible {
        outline-width: 0px;
        outline-offset: 0px;
      }

      &.adyen-checkout__payment-method--selected {
        border: 1px solid var(--adyen-sdk-color-outline-primary-active);

        .adyen-checkout__payment-method__details {
          padding: 0;
        }

        .adyen-checkout__payment-method__details__content {
          padding: 0 1rem;
        }

        &:hover {
          box-shadow: none;
        }

        &.adyen-checkout__payment-method--standalone {
          border: none;
          padding: 0rem;

          .adyen-checkout__spinner,
          .adyen-checkout__spinner__wrapper {
            height: auto;
            gap: 0;
          }

          .loading-input__spinner,
          .adyen-checkout__spinner__wrapper,
          .adyen-checkout__spinner {
            justify-content: initial;
            flex-direction: column;
            align-items: initial;

            &:before,
            &:after {
              content: '';
              height: 60px;
              width: 100%;
              display: block;
              animation: pulseBlock 2s ease-in-out infinite;
              background-color: var(--adyen-sdk-color-outline-primary);
            }
          }

          .loading-input__spinner,
          .adyen-checkout__spinner {
            &:before {
              border-radius: 10px;
              width: 70px;
              height: 20px;
              margin-bottom: 5px;
            }
          }

          .adyen-checkout__spinner__wrapper {
            &:after {
              border-radius: 10px;
              width: 70px;
              height: 20px;
              margin-bottom: 5px;
            }
          }

          .adyen-checkout__spinner {
            padding-top: 44px;
            padding-bottom: 41px;
          }

          .adyen-checkout__payment-method__header {
            padding: 0;
          }

          .adyen-checkout__payment-method__details__content > :last-child {
            margin-bottom: 0.5rem;
          }

          .adyen-checkout__payment-method__details__content {
            padding: 0;
          }
        }
      }
    }

    .adyen-checkout__checkbox__input:focus + .adyen-checkout__checkbox__label:after {
      border: 2px solid var(--adyen-sdk-color-outline-primary-active);
      box-shadow: 0 0 0 0.5px var(--adyen-sdk-color-outline-primary-active);
    }

    .adyen-checkout__payment-method__image__wrapper {
      width: 30px;

      img {
        width: 35px;
      }
    }

    .adyen-checkout__card__brands__brand-wrapper {
      width: 35px;
      height: 22.75px;
    }

    .adyen-checkout-card-input__icon {
      width: 35px;
      height: 22.75px;
    }

    .adyen-checkout-input__inline-validation--invalid {
      display: none;
    }

    .adyen-checkout__input {
      font-weight: 600;
      font-size: 1em;
    }

    .adyen-checkout__payment-method__name--selected {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--adyen-sdk-color-label-primary);
    }

    .adyen-checkout-form-instruction {
      font-size: 1rem;
      line-height: 1.5;
      font-weight: 400;
      color: var(--adyen-custom-color-text);
    }

    .adyen-checkout-contextual-text {
      font-size: 1rem;
      line-height: 1.5;
      font-weight: 400;
      color: var(--adyen-custom-color-text);
    }

    .adyen-checkout__label__text {
      color: var(--adyen-custom-color-text);
    }

    .adyen-checkout-contextual-text--error {
      font-size: 1.125rem;
      line-height: 1.5;
      color: var(--adyen-custom-color-text);
      background-color: var(--adyen-custom-color-background-text);
      padding: 0.5rem;
      margin-top: 0.75rem;
    }

    .adyen-checkout-contextual-text--error:empty {
      padding: 0;
      margin: 0;
    }

    .adyen-checkout__store-details {
      border: none;
      padding: 12px 0 0 0;
    }
  }
}
