set -eu

REPO_URL="730011650125.dkr.ecr.ap-southeast-2.amazonaws.com"
REPO_NAME="application/qantas-hotels-ui-cypress"

echo "
env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1
  BUILDKIT_PROGRESS: plain

steps:
  - label: ':docker: Build Cypress Image'
    plugins:
      ecr#v2.9.0:
        login: true
        no-include-email: true
      docker-compose#v4.16.0:
        build: cypress
        image-repository: ${REPO_URL}/${REPO_NAME}
        image-name: ${BUILDKITE_BUILD_NUMBER}
        cache-from: cypress:${REPO_URL}/${REPO_NAME}:latest
        args:
          - BUILDKIT_INLINE_CACHE=1

  - wait

  - label: ':docker: Push image tagged with branch'
    plugins:
      ecr#v2.9.0:
        login: true
        no-include-email: true
      docker-compose#v4.16.0:
        push: cypress:${REPO_URL}/${REPO_NAME}:latest

  - wait
"

readonly spec_dir="cypress/e2e/**/*"

for spec in $(find ${spec_dir} -type f -name '*.spec.js'); do
    echo "
  - label: ':cypress: ${spec/${spec_dir}\//}'
    command: 'yarn run test:e2e --spec $spec'
    artifact_paths:
      - cypress/videos/**/*
      - cypress/screenshots/**/*
    concurrency: 8
    concurrency_group: 'qantas_hotels_ui/$BUILDKITE_BUILD_NUMBER/e2e-tests'
    timeout_in_minutes: 7
    retry:
      automatic:
        limit: 2
    plugins:
      docker-compose#v4.16.0:
        run: cypress
    agents:
      ci-env: production
    "
done
