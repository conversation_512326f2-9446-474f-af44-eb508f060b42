import {
  fillAdyenPaymentForm,
  submitPaymentOptionsForm,
  submitPaymentForm,
  submitPersonalDetailsForm,
  submitQantasPointsForm,
  usePoints,
  usePointsAndCash,
} from '../../support/common';

const { findByTestId, findByText } = cy;

describe('Checkout bookings', () => {
  before(() => {
    cy.visitCheckoutWithStayAttributes();
  });

  it('can make a booking with cash', () => {
    submitPersonalDetailsForm();
    submitQantasPointsForm();
    submitPaymentOptionsForm();
    fillAdyenPaymentForm();
    submitPaymentForm();

    cy.checkBookingConfirmation();
    findByTestId('cash-payable-now-amount').should('exist');
    findByTestId('points-amount').should('not.exist');
  });

  it('can make a booking with cash and points', () => {
    cy.visitCheckoutWithStayAttributes();
    cy.loginViaSSO();
    findByText(/Available Qantas Points:/i).should('be.visible');

    submitPersonalDetailsForm();
    submitQantasPointsForm();
    usePointsAndCash();
    fillAdyenPaymentForm();
    submitPaymentForm();

    cy.checkBookingConfirmation();
  });

  it('can make a booking with points', () => {
    cy.visitCheckoutWithStayAttributes();
    cy.loginViaSSO();

    submitPersonalDetailsForm();
    submitQantasPointsForm();
    usePoints();
    submitPaymentForm();

    cy.checkBookingConfirmation(50000);
    findByTestId('cash-payable-now-amount').should('not.exist');

    cy.checkBookingConfirmationPoints();
  });

  it('can make a classic booking', () => {
    cy.visitCheckoutForClassic();

    findByTestId('payment-options-form').should('not.exist');
    findByTestId('qantas-points-form').should('not.exist');

    cy.loginViaSSO();

    submitPersonalDetailsForm();

    submitPaymentForm();

    cy.checkBookingConfirmation(50000);

    // Currently only way to check classic booking in confirmation page
    findByTestId('payment-base-rate').within(() => {
      findByTestId('currency-text-unhidden')
        .invoke('text')
        .then((text) => expect(text).to.match(/PTS/));
    });

    cy.checkBookingConfirmationPoints();
  });
});

describe('Checkout - Quote Unavailable Scenarios: ', () => {
  describe('When visiting the checkout page: ', () => {
    describe('with dates too far into the future', () => {
      it('the quote unavailable message is displayed and clicking the button takes you back to the property page', () => {
        cy.visitCheckoutWithExcessiveFutureDates();

        cy.checkQuoteUnavailable();
      });
    });

    describe('with old dates', () => {
      it('the quote unavailable message is displayed and clicking the button takes you back to the property page', () => {
        cy.visitCheckoutWithOldDates();

        cy.checkQuoteUnavailable();
      });
    });
  });
});
