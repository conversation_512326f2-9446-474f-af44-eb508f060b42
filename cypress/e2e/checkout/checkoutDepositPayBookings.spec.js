import { fillAdyenPaymentForm, submitPaymentForm, submitPersonalDetailsForm } from '../../support/common';

const { findByTestId, findByText } = cy;

describe('Deposit pay bookings', () => {
  beforeEach(() => {
    cy.setupWaitDepositPayQuote();
    cy.setupWaitBooking();
    cy.visitCheckoutForDepositPay();
  });

  it('can make a deposit pay booking with cash', () => {
    submitPersonalDetailsForm();

    findByTestId('qantas-points-form').within(() => {
      findByTestId('next-step-button').click();
    });

    findByTestId('payment-options-form').within(() => {
      findByTestId('deposit-payment-button').click();

      // wait for quoteDepositPay quote request to complete
      cy.wait('@deposit-pay-quote-result').its('request.body.paidByDeposit').should('eq', 'true');
    });

    findByTestId('payment-options-form').within(() => {
      findByText(/continue/i).click();
    });

    fillAdyenPaymentForm();

    submitPaymentForm();

    // wait for depositPay booking request to complete
    cy.wait('@booking-request').its('response.body.paymentSchedule[0].total').should('exist');

    cy.checkBookingConfirmation();
  });

  it('can make a deposit pay booking with points + cash', () => {
    cy.loginViaSSO();
    findByText(/Available Qantas Points:/i).should('be.visible');

    submitPersonalDetailsForm();

    findByTestId('qantas-points-form').within(() => {
      findByTestId('next-step-button').click();
    });

    findByTestId('payment-options-form').within(() => {
      findByTestId('deposit-payment-button').click();

      // wait for quoteDepositPay quote request to complete
      cy.wait('@deposit-pay-quote-result').its('request.body.paidByDeposit').should('eq', 'true');
    });

    findByTestId('payment-options-form').within(() => {
      findByTestId('edit-points-button').click();
    });

    findByTestId('modal-body').within(() => {
      findByTestId('points-input').clear().type('5000{enter}');
    });

    findByTestId('modal-footer').within(() => {
      findByText(/add points/i).click();
    });

    findByTestId('payment-options-form').within(() => {
      findByTestId('edit-cash-button').click();
    });

    findByTestId('modal-body').within(() => {
      findByTestId('cash-input').clear().type('0{enter}');
    });

    findByTestId('modal-footer').within(() => {
      findByText(/add cash/i).click();
    });

    findByTestId('payment-options-form').within(() => {
      findByText(/continue/i).click();
    });

    fillAdyenPaymentForm();

    submitPaymentForm();

    cy.wait('@booking-request').its('response.body.paymentSchedule[0].total').should('exist');

    cy.checkBookingConfirmation(50000);

    findByTestId('points-amount').within(() => {
      findByTestId('amount')
        .invoke('text')
        .then((text) => expect(text).to.match(/5,000/));
    });
  });
});

describe('Deposit Pay Quote Booking', () => {
  before(() => {
    cy.setupStubDepositPayQuote();
    cy.setupWaitBooking();
    cy.visitCheckoutForDepositPay();
  });

  it('can make a deposit pay booking with the correct quote reference', () => {
    submitPersonalDetailsForm();

    findByTestId('qantas-points-form').within(() => {
      findByTestId('next-step-button').click();
    });

    findByTestId('payment-options-form').within(() => {
      findByTestId('deposit-payment-button').click();

      // wait for quoteDepositPayStub request to complete
      cy.wait('@deposit-pay-quote-stub');
    });

    findByTestId('payment-options-form').within(() => {
      findByText(/continue/i).click();
    });

    fillAdyenPaymentForm();

    submitPaymentForm();

    // wait for depositPay booking request to complete
    cy.wait('@booking-request').its('request.body.stays[0].quoteReference').should('include', '474adfcd-ff32-4103-8c6c-612cfd758082');
  });
});
